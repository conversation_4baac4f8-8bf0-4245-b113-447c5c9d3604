name: deploy-to-dentsu-server

on:
  push:
    tags:
      - '[0-9]+.[0-9]+.[0-9]+-dentsufile'

jobs:
  dentsu-aibusinessschool-com:
    name: dentsu-aibusinessschool-com
    runs-on: vm-dentsufile
    steps:
      - name: clone repository
        run: |
          cd /home/<USER>/devops
          sudo rm -rf ${{ github.event.repository.name }}
          sudo git clone --branch dentsu-new https://${{ secrets.ADOPTIONV2 }}@github.com/${{ github.repository_owner }}/${{ github.event.repository.name }}.git
          
      - name: docker build & run
        run: |
          cd /home/<USER>/devops
          sudo docker compose build --build-arg BUILD_ENV=production
          sudo docker compose up -d --force-recreate
          
      - name: list running docker containers
        run: |
          sudo docker ps