import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // Load environment variables
  const env = loadEnv(mode, process.cwd(), '');

  return {
    plugins: [react()],
    resolve: {
      alias: {
        '@': '/src',
        '@components': '/src/components',
        '@domains': '/src/domains',
        '@hooks': '/src/hooks',
        '@utils': '/src/utils',
      },
    },
    define: {
      // Define correct API URL according to environment
      'process.env.VITE_API_URL': JSON.stringify(
        mode === 'development'
          ? env.VITE_API_URL_DEV
          : mode === 'preprod'
            ? env.VITE_API_URL_PREPROD
            : env.VITE_API_URL_PROD
      ),
      'process.env.VITE_CDS_API_URL': JSON.stringify(
        mode === 'development'
          ? env.VITE_CDS_API_URL_DEV
          : mode === 'preprod'
            ? env.VITE_CDS_API_URL_PREPROD
            : env.VITE_CDS_API_URL_PROD
      ),
    },
    preview: {
      allowedHosts: [
        'client',
        'localhost',
        'adoptionv2dev.aibusinessschool.com',
        'adoptionv2preprod.aibusinessschool.com',
        'dentsu.aibusinessschool.com',
      ],
    },
    server: { host: true, port: 5173 },
  };
});
