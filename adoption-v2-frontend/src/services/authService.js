import axios from 'axios';
import { CONFIG } from '../config-global';

const API_BASE_URL = CONFIG.apiUrl;

export const authService = {
  // Logout function
  logout: async () => {
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/logout`);

      return response.data;
    } catch (error) {
      console.error('Logout error:', error);
      console.error('Logout error response:', error.response?.data);
      throw error;
    }
  },

  // Admin function to logout user
  logoutUser: async (userId) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/admin/logout/${userId}`);
      return response.data;
    } catch (error) {
      console.error('Admin logout user error:', error);
      throw error;
    }
  },

  // Get active sessions (for admin)
  getActiveSessions: async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/auth/admin/sessions`);
      return response.data;
    } catch (error) {
      console.error('Get active sessions error:', error);
      throw error;
    }
  },
};

export default authService;
