import { useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import useUpdateJourneyTracking from '../utils/updateJourneyTracking';
import { useGetUseCaseByIdQuery } from '../../../redux/services/use-case-api';
import { useFormByIdQuery } from '../../../redux/services/form-service';
import { useSubmitFormResponseMutation } from '../../../redux/services/form-response';
import { useLevelsQuery } from '../../../redux/services/cds-api';
import { useDispatch } from 'react-redux';
import { setCurrentCardId } from '../../../redux/features/courses/courseSlice';
import { useTranslation } from 'react-i18next';
import { IDEA_FORM_ID } from '../../../constants/form-constants';
import useUserRole from '@/hooks/useUserRole';
import {
  calculateHighestCompletedPosition,
  isJourneyCompletedByPosition,
  sortJourneysByOrder,
} from '../utils/journeyCompletionUtils.js';

/**
 * Journey card handlers hook
 * @param {Object} props - Hook parameters
 * @param {Object} props.trackingData - User's journey tracking data
 * @param {string} props.selectedLevel - Selected journey level
 * @param {Object} props.user - User information
 * @param {Array} props.journeyData - Journey data array (optional, for positional completion)
 * @returns {Object} Journey card handlers
 */
const useJourneyCardHandlers = ({ trackingData, selectedLevel, user, journeyData = null }) => {
  const navigate = useNavigate();
  const { i18n } = useTranslation();
  const { updateJourneyTrackingCard } = useUpdateJourneyTracking();

  // Use userRole hook
  const { isAdministrator, isLimitedUser, getUserJourneyLevel } = useUserRole();

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalContent, setModalContent] = useState({
    title: '',
    content: '',
    cardId: null,
    isCompleted: false,
    subtitles: null,
  });
  const [currentUseCaseId, setCurrentUseCaseId] = useState(null);
  const [pendingNavigation, setPendingNavigation] = useState(null);
  const [currentJourneyCardId, setCurrentJourneyCardId] = useState(null);

  // Ideation form states
  const [isIdeationModalOpen, setIsIdeationModalOpen] = useState(false);
  const [ideationFormId, setIdeationFormId] = useState(null);
  const [ideationCardId, setIdeationCardId] = useState(null);
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({});
  const [formErrors, setFormErrors] = useState({});

  // Fetch levels data from Levels API
  const { data: levelsData } = useLevelsQuery();

  const currentLanguage = i18n.language || user?.onboarding?.language?.slug || 'en';

  // Define dispatch
  const dispatch = useDispatch();

  // Get current level modal content
  const getCurrentLevelModalContent = useCallback(
    (level, getPreviousLevel = false) => {
      if (!levelsData?.data) return null;

      // Ensure level value is valid
      if (!level) return null;

      // Level order
      const levelOrder = ['beginner', 'expert', 'master'];

      // Determine target level slug
      let targetLevelSlug = level.toLowerCase();

      // If we need previous level content
      if (getPreviousLevel) {
        const currentIndex = levelOrder.indexOf(targetLevelSlug);

        // If valid level and not first level
        if (currentIndex > 0) {
          // Get previous level
          targetLevelSlug = levelOrder[currentIndex - 1];
        }
      }

      // Find target level data
      const targetLevelData = levelsData.data.find((level) => level.slug === targetLevelSlug);

      if (!targetLevelData) {
        return null;
      }

      // Check content by language
      let content = '';

      // Language order - first preferred language, then English
      const languagePreferences = [`${currentLanguage}_modal`, 'en_modal', 'de_modal'];

      // Find content by language preferences
      for (const langKey of languagePreferences) {
        if (targetLevelData.translations?.[langKey]) {
          content = targetLevelData.translations[langKey];
          break;
        }
      }

      // If content not found, check modalContent
      if (!content && targetLevelData.modalContent?.length > 0) {
        content = targetLevelData.modalContent[0];
      }

      // Return result object
      const modalData = {
        content,
        levelSlug: targetLevelData.slug,
        nextLevel: targetLevelData.nextLevel,
        isLastLevel: targetLevelData.isLastLevel,
        levelData: targetLevelData,
      };

      return modalData;
    },
    [levelsData, currentLanguage]
  );

  /**
   * Check if a value is empty
   */
  const checkIfEmpty = useCallback((value) => {
    if (value === undefined || value === null) {
      return true;
    }

    if (typeof value === 'string') {
      return value.trim() === '';
    }

    if (Array.isArray(value)) {
      return value.length === 0;
    }

    if (typeof value === 'object') {
      return Object.keys(value).length === 0;
    }

    return false;
  }, []);

  // Get user's language preference - First from i18n, then from user
  const userLanguage = i18n.language || user?.onboarding?.language?.slug || 'en';

  // Fetch ideation form - Use user's active language preference
  const { data: ideationFormData, isLoading: isIdeationFormLoading } = useFormByIdQuery(
    {
      formId: ideationFormId,
      lang: userLanguage,
    },
    {
      skip: !ideationFormId,
      // Refetch form when language changes
      refetchOnMountOrArgChange: true,
    }
  );

  // Form submission
  const [submitFormResponse, { isLoading: isSubmittingForm }] = useSubmitFormResponseMutation();

  // Fetch use case data
  const { data: useCaseData } = useGetUseCaseByIdQuery(currentUseCaseId, {
    skip: !currentUseCaseId,
  });

  // Navigate when useCaseData changes
  useEffect(() => {
    if (pendingNavigation && useCaseData) {
      if (useCaseData.slug) {
        // Send journey tracking parameters when navigating to usecase page
        navigate(`/usecase/${useCaseData.slug}`, {
          state: {
            journeyCardId: currentJourneyCardId,
            journeyTrackingData: trackingData,
            journeyLevel: selectedLevel,
          },
        });
      } else {
        // Navigate with ID if slug is not available
        navigate(`/usecase/${pendingNavigation}`, {
          state: {
            journeyCardId: currentJourneyCardId,
            journeyTrackingData: trackingData,
            journeyLevel: selectedLevel,
          },
        });
      }
      setPendingNavigation(null);
    }
  }, [useCaseData, pendingNavigation, navigate, currentJourneyCardId, trackingData, selectedLevel]);

  const isJourneyCompleted = useCallback(
    (cardId) => {
      if (!trackingData || !selectedLevel) return false;

      const completedJourneyIds = trackingData[selectedLevel.toLowerCase()] || [];

      // If journey data is available, use positional completion logic
      if (journeyData && journeyData.length > 0) {
        // Sort journeys by order
        const sortedJourneys = sortJourneysByOrder(journeyData);

        // Find the specific journey
        const journey = sortedJourneys.find((j) => j._id === cardId);
        if (!journey) {
          // If journey not found in current data, fall back to simple ID check
          return completedJourneyIds.includes(cardId);
        }

        // Calculate highest completed position
        const highestCompletedPosition = calculateHighestCompletedPosition(
          sortedJourneys,
          completedJourneyIds
        );

        // Check if this journey should be considered completed
        return isJourneyCompletedByPosition(journey, highestCompletedPosition, completedJourneyIds);
      }

      // Fall back to simple ID check if journey data is not available
      return completedJourneyIds.includes(cardId);
    },
    [trackingData, selectedLevel, journeyData]
  );

  const isPreviousCompleted = useCallback(
    (index, journeyData) => {
      // Check user's actual level with selected level
      const userJourneyLevel = getUserJourneyLevel();
      const currentSelectedLevel = selectedLevel?.toLowerCase();

      // If user is Administrator, always grant access
      if (isAdministrator()) {
        return true;
      }

      // If user is limited-use and selected level is not beginner, lock all cards
      if (isLimitedUser() && currentSelectedLevel !== 'beginner') {
        return false;
      }

      // If user's actual level is not the same as selected level, lock all cards
      if (userJourneyLevel !== currentSelectedLevel) {
        return false;
      }

      // First card should always be accessible (only for user's actual level)
      if (index === 0) return true;

      // For other cards, check previous card's completion status
      if (!journeyData || !journeyData[index - 1]) return false;
      return isJourneyCompleted(journeyData[index - 1]._id);
    },
    [isJourneyCompleted, selectedLevel, isAdministrator, isLimitedUser, getUserJourneyLevel]
  );

  const getFirstClickableCardIndex = useCallback(
    (journeyData) => {
      if (!journeyData) return 0;

      // If user is Administrator, always show first card
      if (isAdministrator()) {
        return 0;
      }

      // Check user's actual level with selected level
      const userJourneyLevel = getUserJourneyLevel();
      const currentSelectedLevel = selectedLevel?.toLowerCase();

      // If user is limited-use and selected level is not beginner, show first card but lock it
      if (isLimitedUser() && currentSelectedLevel !== 'beginner') {
        return 0;
      }

      // If user's actual level is not the same as selected level, show first card but lock it
      if (userJourneyLevel !== currentSelectedLevel) {
        return 0;
      }

      // Find last completed card
      let lastCompletedIndex = -1;
      for (let i = 0; i < journeyData.length; i++) {
        if (isJourneyCompleted(journeyData[i]._id)) {
          lastCompletedIndex = i;
        } else {
          break;
        }
      }

      // Return next card after last completed card or first card
      return lastCompletedIndex + 1 < journeyData.length ? lastCompletedIndex + 1 : 0;
    },
    [isJourneyCompleted, selectedLevel, isAdministrator, isLimitedUser, getUserJourneyLevel]
  );

  const getTranslationKey = useCallback((currentLanguage, userLanguage) => {
    if (currentLanguage === 'de') return 'german';
    if (userLanguage?.slug === 'german') return 'german';
    return 'english';
  }, []);

  const handleCardClick = useCallback(
    (item, language) => {
      // If card is completed or user is Administrator, it should always be clickable
      const isCardCompleted = isJourneyCompleted(item._id);

      // Check user's actual level with selected level
      // If card is completed or user is Administrator, skip level check
      if (
        !isCardCompleted &&
        !isAdministrator() &&
        !item.levels?.some((level) => user?.journeyLevel?.skippedJourney?.includes(level))
      ) {
        const userJourneyLevel = getUserJourneyLevel();
        const currentSelectedLevel = selectedLevel?.toLowerCase();

        // If user is limited-use and selected level is not beginner, cards should not be clickable
        if (isLimitedUser() && currentSelectedLevel !== 'beginner') {
          return;
        }

        // If user's actual level is not the same as selected level, cards should not be clickable
        if (userJourneyLevel !== currentSelectedLevel) {
          return;
        }
      }

      const translationKey = language === 'de' ? 'german' : 'english';
      const selectedTranslation =
        item.translations?.[translationKey] || item.translations?.english || {};

      // If journeyType is "Usecases" and selectedFunctionSpecUseCases exists
      if (item.journeyType === 'Usecases' && item.selectedFunctionSpecUseCases) {
        // Set use case ID and keep for navigation
        setCurrentUseCaseId(item.selectedFunctionSpecUseCases);
        setPendingNavigation(item.selectedFunctionSpecUseCases);
        setCurrentJourneyCardId(item._id);

        return;
      }

      // If journeyType is "Ideation Project" and ideation exists
      if (item.journeyType === 'Ideation Project' && item.ideation) {
        // Use fixed IDEA_FORM_ID, instead of item.ideation value
        setIdeationFormId(IDEA_FORM_ID);
        setIdeationCardId(item._id);
        setIsIdeationModalOpen(true);
        return;
      }

      // For App Creator type cards, navigate
      if (item.journeyType === 'App Creator') {
        // Use ButtonURL if available, otherwise use default navigation
        const targetUrl = item.buttonURL || '/app-creator';
        navigate(targetUrl, {
          state: { cardId: item._id },
        });
        return;
      }

      // For Workflow Creator type cards, navigate
      if (item.journeyType === 'Workflow Creator') {
        // Use ButtonURL if available, otherwise use default navigation
        const targetUrl = item.buttonURL || '/workflow-creator';
        navigate(targetUrl, {
          state: { cardId: item._id },
        });
        return;
      }

      // For GenAI Playground type cards, navigate
      if (item.journeyType === 'GenAI Playground') {
        // Use ButtonURL if available, otherwise use default navigation
        const targetUrl = item.buttonURL || '/genai-playground';
        navigate(targetUrl, {
          state: { cardId: item._id },
        });
        return;
      }

      // For Content type cards, open modal
      if (item.journeyType === 'Content' || item.journeyType === 'content') {
        setModalContent({
          title: selectedTranslation.title || item.title,
          content: selectedTranslation.content || item.content,
          cardId: item._id,
          isCompleted: isJourneyCompleted(item._id),
          subtitles: item.subtitles || null,
        });
        setIsModalOpen(true);
        return;
      }

      // For Course type cards, navigate
      if (item.journeyType === 'Course' || item.journeyType === 'course') {
        // Save cardId to Redux
        dispatch(setCurrentCardId(item._id));

        // Debug information

        // Save journey information to sessionStorage (for state loss)
        sessionStorage.setItem('journeyCardId', item._id);
        sessionStorage.setItem('journeyLevel', selectedLevel);
        sessionStorage.setItem('journeyCardId_courseId', item.courseId); // Save courseId

        // Navigate to course page, add cardId to URL
        navigate(`/course/${item.course}`, {
          state: {
            journeyCardId: item._id,
            journeyTrackingData: trackingData,
            journeyLevel: selectedLevel,
          },
        });
        return;
      }

      // Check buttonType
      if (item.buttonType === 'Selected Content' || item.buttonType?.toLowerCase() === 'selected') {
        setModalContent({
          title: selectedTranslation.title || item.title,
          content: selectedTranslation.content || item.content,
          cardId: item._id,
          isCompleted: isJourneyCompleted(item._id),
          subtitles: item.subtitles || null,
        });
        setIsModalOpen(true);
      } else if (item.buttonType?.toLowerCase() === 'url') {
        if (item.newTab) {
          window.open(item.buttonURL, '_blank');
        } else {
          navigate(item.buttonURL);
        }
      } else if (item.buttonType?.toLowerCase() === 'course') {
        // Add cardId for Course buttonType
        dispatch(setCurrentCardId(item._id));

        // Debug information

        // Save journey information to sessionStorage (for state loss)
        sessionStorage.setItem('journeyCardId', item._id);
        sessionStorage.setItem('journeyLevel', selectedLevel);
        sessionStorage.setItem('journeyCardId_courseId', item.course); // Save courseId

        // Navigate to course page, add cardId to URL
        navigate(`/course/${item.courseId}`, {
          state: {
            journeyCardId: item._id,
            journeyTrackingData: trackingData,
            journeyLevel: selectedLevel,
          },
        });
      }
    },
    [
      navigate,
      isJourneyCompleted,
      setCurrentUseCaseId,
      setPendingNavigation,
      setCurrentJourneyCardId,
      selectedLevel,
      dispatch,
      isAdministrator,
      isLimitedUser,
      getUserJourneyLevel,
    ]
  );

  const handleCardClickTechCompany = useCallback((url) => {
    window.open(url, '_blank');
  }, []);

  const handleModalClose = useCallback(
    async (result, refetchTracking, refetchJourneys, setIsInitialized) => {
      setIsModalOpen(false);

      if (result && result.completed && modalContent.cardId && !modalContent.isCompleted) {
        try {
          // Use selected level, not user's profile level
          await updateJourneyTrackingCard({
            userId: user?._id,
            journeyTrackingData: trackingData,
            userLevel: selectedLevel?.toLowerCase(), // Use selected level
            cardId: modalContent.cardId,
          });

          // Refresh data
          if (refetchTracking) {
            await refetchTracking();
          }
          if (refetchJourneys) {
            await refetchJourneys();
          }
          if (setIsInitialized) {
            setIsInitialized(false);
          }
        } catch {
          console.error('Error updating journey tracking card');
        }
      }
    },
    [modalContent, user, trackingData, selectedLevel, updateJourneyTrackingCard]
  );

  const handleIdeationFormSubmit = useCallback(
    async (refetchTracking, refetchJourneys) => {
      // Check if ideationFormData is loaded
      console.warn(
        'handleIdeationFormSubmit çağrıldı - ideationFormData durumu:',
        !!ideationFormData
      );

      if (!ideationFormData) {
        console.error('ideationFormData henüz yüklenmedi, form gönderilemiyor.');
        return false;
      }
      try {
        // Debug information
        console.warn('Try block ideationFormData status:', !!ideationFormData);

        // Prepare form fields
        let formFields = [];
        // Check new API response structure
        if (ideationFormData?.data?.fields && ideationFormData.data.fields.length > 0) {
          formFields = ideationFormData.data.fields;
        } else if (ideationFormData?.fields && ideationFormData.fields.length > 0) {
          formFields = ideationFormData.fields;
        } else if (ideationFormData?.data?.forms && ideationFormData.data.forms[0]?.fields) {
          formFields = ideationFormData.data.forms[0].fields;
        } else if (ideationFormData?.data?.topics && ideationFormData.data.topics.length > 0) {
          // Merge all topics fields
          formFields = [];
          ideationFormData.data.topics.forEach((topic) => {
            if (topic.fields && topic.fields.length > 0) {
              formFields = [...formFields, ...topic.fields];
            }
          });
          console.warn('All fields from topics merged, found fields:', formFields.length);
        } else {
          formFields = [];
        }

        // Check if form fields are empty
        if (formFields.length === 0) {
          developmentError('No form fields found in API response');
          return false;
        }

        // Prepare
        const responses = formFields
          .filter((field) => !checkIfEmpty(formData[field.name]))
          .map((field) => ({
            fieldId: field._id,
            name: field.label.toLowerCase(),
            type: field.type,
            value: formData[field.name],
          }));

        // Determine source name by journey level
        const journeyLevel =
          selectedLevel?.toLowerCase() || user?.journeyLevel?.name?.toLowerCase() || 'unknown';
        const sourceType = `${journeyLevel}-journey`;

        // Submit form data
        const response = await submitFormResponse({
          formId: ideationFormId,
          formType: 'ideation',
          title: ideationFormData?.data?.title || ideationFormData?.title || 'Ideation Form',
          description: ideationFormData?.data?.description || ideationFormData?.description || '',
          responses,
          source: sourceType,
          sourceDetails: {
            cardId: ideationCardId,
            journeyLevel: journeyLevel,
          },
          lang: userLanguage,
        }).unwrap();

        // If successful response, update journey tracking
        if (response && (response.status === 'success' || response._id)) {
          // Update journey tracking - Use selected level, not user's profile level
          await updateJourneyTrackingCard({
            userId: user?._id,
            journeyTrackingData: trackingData,
            userLevel: selectedLevel?.toLowerCase(), // Use selected level
            cardId: ideationCardId,
          });

          // Refresh data
          if (refetchTracking) {
            await refetchTracking();
          }
          if (refetchJourneys) {
            await refetchJourneys();
          }

          // Close modal and clear form data
          setIsIdeationModalOpen(false);
          setFormData({});
          setFormErrors({});
          setActiveStep(0);
          setIdeationFormId(null);
          setIdeationCardId(null);

          return true;
        }

        return false;
      } catch (error) {
        developmentError('Form submission error:', error);
        return false;
      }
    },
    [
      ideationFormData,
      formData,
      user,
      trackingData,
      selectedLevel,
      ideationCardId,
      ideationFormId,
      setIsIdeationModalOpen,
      setFormData,
      setFormErrors,
      setActiveStep,
      updateJourneyTrackingCard,
      submitFormResponse,
      checkIfEmpty,
      userLanguage,
    ]
  );

  /**
   * Close ideation form
   */
  const handleIdeationModalClose = useCallback(() => {
    setIsIdeationModalOpen(false);
    setFormData({});
    setFormErrors({});
    setActiveStep(0);
    setIdeationFormId(null);
    setIdeationCardId(null);
  }, [
    setIsIdeationModalOpen,
    setFormData,
    setFormErrors,
    setActiveStep,
    setIdeationFormId,
    setIdeationCardId,
  ]);

  /**
   * Function to run when form data changes
   */
  const handleFormChange = useCallback(
    (newData) => {
      if (!newData || Object.keys(newData).length === 0) {
        return;
      }

      if (newData.__errors) {
        const updatedErrors = { ...formErrors };

        Object.entries(newData.__errors).forEach(([key, value]) => {
          if (value === null) {
            // Clear error
            delete updatedErrors[key];
          } else {
            // Add new error
            updatedErrors[key] = value;
          }
        });

        // Update
        setFormErrors(updatedErrors);
        delete newData.__errors;
      }

      // Form data
      if (newData.__formState) {
        // Update all form data directly
        setFormData(newData.__formState);
        delete newData.__formState;
      } else {
        // Clear __userInteracted and __formState special fields
        delete newData.__userInteracted;

        // Update form data
        const updatedFields = Object.keys(newData).filter((k) => k !== '__errors');
        if (updatedFields.length > 0) {
          const updatedFormData = { ...formData };

          updatedFields.forEach((fieldName) => {
            updatedFormData[fieldName] = newData[fieldName];

            // If field is filled and there is an error, clear error
            if (!checkIfEmpty(newData[fieldName]) && formErrors[fieldName]) {
              const updatedErrors = { ...formErrors };
              delete updatedErrors[fieldName];
              setFormErrors(updatedErrors);
            }
          });

          setFormData(updatedFormData);
        }
      }
    },
    [formData, formErrors, checkIfEmpty]
  );

  return {
    isModalOpen,
    setIsModalOpen,
    modalContent,
    setModalContent,
    isJourneyCompleted,
    isPreviousCompleted,
    getFirstClickableCardIndex,
    getTranslationKey,
    handleCardClick,
    handleCardClickTechCompany,
    handleModalClose,
    useCaseData,
    currentUseCaseId,
    // Ideation form
    isIdeationModalOpen,
    handleIdeationModalClose,
    handleIdeationFormSubmit,
    ideationFormData,
    isIdeationFormLoading,
    isSubmittingForm,
    formData,
    formErrors,
    activeStep,
    setActiveStep,
    handleFormChange,
    // Levels API
    getCurrentLevelModalContent,
  };
};

export default useJourneyCardHandlers;
