import { useState, useEffect, useCallback } from 'react';
import { useUpdateUserJourneyLevelMutation } from '@/redux/services/user-api';
import {
  useUpdateSeenModalsMutation,
  useGetJourneyTrackingQuery,
  useGetJourneysQuery,
} from '@/redux/services/journey-api';
import { useDispatch, useSelector } from 'react-redux';
import { updateUser } from '@/redux/features/auth/authSlice';
import { setSelectedLevel } from '@/redux/features/training/trainingSlice';

/**
 * Hook that manages journey level upgrade operations
 * @returns {Object} Hook functions and states
 */
const useJourneyLevelUpgrade = () => {
  const [updateUserJourneyLevel] = useUpdateUserJourneyLevelMutation();
  const [updateSeenModals] = useUpdateSeenModalsMutation();
  const user = useSelector((state) => state.auth.user);
  const dispatch = useDispatch();

  // States
  const [showConfetti, setShowConfetti] = useState(false);
  const [showCongratulations, setShowCongratulations] = useState(false);
  const [pendingLevelUpgrade, setPendingLevelUpgrade] = useState(null);
  const [processingAction, setProcessingAction] = useState(false);
  const [completedLevel, setCompletedLevel] = useState(null);
  const [pendingModalKeys, setPendingModalKeys] = useState(new Set());

  // Fetch user's journey tracking data
  const { data: trackingData, refetch: refetchTracking } = useGetJourneyTrackingQuery(user?._id, {
    skip: !user?._id,
  });

  /**
   * Provides consistent format for modal keys
   */
  const getModalKey = useCallback((level, action) => {
    return `${level.toLowerCase()}_${action}`;
  }, []);

  /**
   * Sends API request to save user's seen modals
   */
  const updateModalSeen = useCallback(
    async (userId, level, action) => {
      if (!userId) return;

      try {
        const modalKey = getModalKey(level, action);

        // Update if modal hasn't been seen before AND not currently pending
        if (!trackingData?.seenModals?.includes(modalKey) && !pendingModalKeys.has(modalKey)) {
          // Add to pending list (to prevent duplicates)
          setPendingModalKeys((prev) => new Set([...prev, modalKey]));

          await updateSeenModals({
            userId,
            modalKey,
          });

          // Update tracking data
          refetchTracking();

          // Remove from pending list
          setPendingModalKeys((prev) => {
            const newSet = new Set([...prev]);
            newSet.delete(modalKey);
            return newSet;
          });
        }
      } catch (error) {
        // Remove from pending list on error
        const modalKey = getModalKey(level, action);
        setPendingModalKeys((prev) => {
          const newSet = new Set([...prev]);
          newSet.delete(modalKey);
          return newSet;
        });
      }
    },
    [trackingData, updateSeenModals, refetchTracking, getModalKey, pendingModalKeys]
  );

  /**
   * Upgrades user's journey level
   */
  const performLevelUpgrade = useCallback(
    async (fromLevel) => {
      if (!user?._id || processingAction) return false;

      setProcessingAction(true);

      try {
        // Don't upgrade if already at master level
        if (fromLevel.toLowerCase() === 'master') {
          setProcessingAction(false);
          return false;
        }

        // Determine next level
        const nextLevel = fromLevel.toLowerCase() === 'beginner' ? 'expert' : 'master';

        // Level upgrade API request
        const response = await updateUserJourneyLevel({
          userId: user._id,
          journeyLevel: {
            name: nextLevel,
            translations: {
              en: nextLevel.charAt(0).toUpperCase() + nextLevel.slice(1),
              de: nextLevel === 'expert' ? 'Experte' : 'Meister',
              tr: nextLevel === 'expert' ? 'Uzman' : 'Usta',
            },
          },
        }).unwrap();

        if (response?.status === 'success') {
          // Update user info in Redux store
          dispatch(
            updateUser({
              ...user,
              journeyLevel: {
                name: nextLevel,
              },
            })
          );

          // Update selected level
          dispatch(setSelectedLevel(nextLevel));

          // Save upgrade record
          updateModalSeen(user._id, fromLevel, 'level_upgraded');

          setProcessingAction(false);
          return true;
        }

        setProcessingAction(false);
        return false;
      } catch (error) {
        setProcessingAction(false);
        return false;
      }
    },
    [user, processingAction, updateUserJourneyLevel, dispatch, updateModalSeen]
  );

  /**
   * Closes congratulations modal
   */
  const handleCloseCongratulations = useCallback(() => {
    setShowConfetti(false);
    setShowCongratulations(false);

    // If there's a completed level, save that completion modal was seen
    if (completedLevel && !processingAction && user?._id) {
      // Save that completion modal was seen
      updateModalSeen(user._id, completedLevel, 'completion');
    }

    // Clear completed level
    setCompletedLevel(null);
  }, [processingAction, completedLevel, updateModalSeen, user]);

  /**
   * Modal display control - independent from level upgrade
   */
  const checkAndShowModal = useCallback(
    ({ journeyData, trackingData, selectedLevel, user }) => {
      if (
        !journeyData?.length ||
        !trackingData ||
        !selectedLevel ||
        !user?._id ||
        processingAction
      ) {
        return false;
      }

      // Basic variables
      const levelKey = selectedLevel.toLowerCase();
      const userLevel = user?.journeyLevel?.name?.toLowerCase();
      const journeyCardCount = journeyData.length;
      const completedCount = (trackingData[levelKey] || []).length;
      const allCardsCompleted = completedCount >= journeyCardCount && journeyCardCount > 0;

      // Modal control keys
      const completionKey = getModalKey(levelKey, 'completion');
      const hasSeenCompletionModal = trackingData?.seenModals?.includes(completionKey);

      // ========== MODAL LOGIC ==========
      // 1. Cards completed
      // 2. User is on this level OR admin
      // 3. Completion modal not shown before

      if (allCardsCompleted && !hasSeenCompletionModal) {
        const isUserOnSameLevel = userLevel === levelKey;
        const isAdmin = user?.role?.toLowerCase() === 'administrator';

        if (isUserOnSameLevel || isAdmin) {
          // Show modal
          setCompletedLevel(levelKey);
          setShowConfetti(true);
          setShowCongratulations(true);

          return true; // Modal shown
        }
      }

      return false; // Modal not shown
    },
    [processingAction, getModalKey]
  );

  /**
   * Level upgrade control - independent from modal
   */
  const checkAndUpgradeLevel = useCallback(
    ({ journeyData, trackingData, selectedLevel, user }) => {
      if (
        !journeyData?.length ||
        !trackingData ||
        !selectedLevel ||
        !user?._id ||
        processingAction
      ) {
        return false;
      }

      // Basic variables
      const levelKey = selectedLevel.toLowerCase();
      const userLevel = user?.journeyLevel?.name?.toLowerCase();
      const journeyCardCount = journeyData.length;
      const completedCount = (trackingData[levelKey] || []).length;
      const allCardsCompleted = completedCount >= journeyCardCount && journeyCardCount > 0;

      // Level upgrade control
      const levelUpgradedKey = getModalKey(levelKey, 'level_upgraded');
      const hasLevelUpgraded = trackingData?.seenModals?.includes(levelUpgradedKey);

      // ========== LEVEL UPGRADE LOGIC ==========
      // 1. Cards completed?
      // 2. User is on this level?
      // 3. Level not upgraded yet?

      if (allCardsCompleted && userLevel === levelKey && !hasLevelUpgraded) {
        // Only upgrade from beginner and expert
        const canUpgrade = levelKey === 'beginner' || levelKey === 'expert';

        if (canUpgrade) {
          performLevelUpgrade(levelKey);
          return true; // Upgrade done
        }
      }

      return false; // Upgrade not done
    },
    [processingAction, getModalKey, performLevelUpgrade]
  );

  /**
   * Main function: both modal and level upgrade control
   */
  const checkAndHandleLevelCompletion = useCallback(
    (params) => {
      // First check modal
      const modalShown = checkAndShowModal(params);

      // Then check level upgrade (independent from modal)
      const levelUpgraded = checkAndUpgradeLevel(params);

      return { modalShown, levelUpgraded };
    },
    [checkAndShowModal, checkAndUpgradeLevel]
  );

  // Hook output
  return {
    showConfetti,
    showCongratulations,
    completedLevel,
    handleCloseCongratulations,
    checkAndUpgradeLevel: checkAndHandleLevelCompletion,
  };
};

export default useJourneyLevelUpgrade;
