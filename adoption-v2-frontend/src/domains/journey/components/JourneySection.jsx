import { useEffect, useRef, useState } from 'react';
import { Box, Button, Typography, CircularProgress } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useSelector, useDispatch } from 'react-redux';
import PropTypes from 'prop-types';
import Carousel from '../../../components/Carousel/Carousel.jsx';
import WhiteContainer from '../../../components/WhiteContainer/WhiteContainer.jsx';
import WelcomeHeader from '../../../components/WelcomeHeader/WelcomeHeader.jsx';
import JourneyCard, { CardContent } from './JourneyCard';
import {
  setSelectedLevel,
  selectSelectedLevel,
} from '../../../redux/features/training/trainingSlice';
import {
  useGetJourneysQuery,
  useGetJourneyTrackingQuery,
  useUpdateJourneyTrackingMutation,
} from '../../../redux/services/journey-api';
import { updateUser } from '../../../redux/features/auth/authSlice';
import useJourneyCardHandlers from '../hooks/useJourneyCardHandlers';
import useJourneyLevelUpgrade from '../hooks/useJourneyLevelUpgrade';
import useJourneyProgress from '../hooks/useJourneyProgress';
import useJourneyLevelManagement from '../hooks/useJourneyLevelManagement';
import useSkipJourney from '../hooks/useSkipJourney';
import SkipJourneyModal from './SkipJourneyModal';
import AIModal from '../../../components/AIModal/AIModal';
import FormModal from '../../../components/FormModal/FormModal';
import FormRender from '../../../components/FormRender/FormRender';
import { processGenAICard } from '../utils/genAICard.js';
import { updateTrackingWithAutoCompletedCards } from '../utils/journeyCompletionUtils.js';
import './JourneyCard.scss';

const JourneySection = ({
  user,
  showSkipJourney = false,
  onSkipJourney = null,
  containerTitle = '',
  hideSection = false,
  showUserProgress = false,
  handleJourneyLevelUpdate = null,
}) => {
  const { t, i18n } = useTranslation();
  const selectedLevel = useSelector(selectSelectedLevel);
  const dispatch = useDispatch();
  const carouselRef = useRef(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [openSkipJourneyConfirmDialog, setOpenSkipJourneyConfirmDialog] = useState(false);

  // Aktif journey kartı takip etmek için state
  const [activeJourneyCard, setActiveJourneyCard] = useState(null);

  // GenAI kart güncellemesinin durumunu takip etmek için ref kullanıyoruz
  const genAICardProcessingRef = useRef(false);

  // Skip journey hook'unu kullan
  const { skipToLevel } = useSkipJourney();

  // Journey tracking mutation
  const [updateJourneyTracking] = useUpdateJourneyTrackingMutation();

  // Journey level management hook
  useJourneyLevelManagement({
    user,
    selectedLevel,
    onJourneyLevelUpdate: handleJourneyLevelUpdate,
  });

  // Journey level upgrade hook'unu kullan
  const { checkAndUpgradeLevel } = useJourneyLevelUpgrade();

  // Journey verilerini ve tracking verilerini çekiyoruz
  const {
    data: journeyData,
    isLoading: isJourneyLoading,
    refetch: refetchJourneys,
  } = useGetJourneysQuery({
    function: user?.onboarding?.function_label,
    levels: selectedLevel,
    managementRole: user?.onboarding?.management_role_label?.slug,
  });

  // User ID kontrolü
  const { data: trackingData, refetch: refetchTracking } = useGetJourneyTrackingQuery(user?._id, {
    skip: !user?._id || !user,
    refetchOnMountOrArgChange: true,
  });

  // Kullanıcı ilerleme durumunu custom hook ile al
  const { userActualLevel, userActualCompletedSteps, userActualProgress, userActualTotalSteps } =
    useJourneyProgress({
      user,
      trackingData,
      showUserProgress,
    });

  // JourneyCard işlemleri için hook'u kullan
  const {
    isModalOpen,
    modalContent,
    isJourneyCompleted,
    isPreviousCompleted,
    getFirstClickableCardIndex,
    getTranslationKey,
    handleCardClick,
    handleModalClose: handleJourneyModalClose,
    // Ideation form için gerekli prop ve fonksiyonlar
    isIdeationModalOpen,
    handleIdeationModalClose,
    handleIdeationFormSubmit,
    ideationFormData,
    isIdeationFormLoading,
    isSubmittingForm,
    formData,
    formErrors,
    activeStep,
    handleFormChange,
  } = useJourneyCardHandlers({
    trackingData,
    selectedLevel,
    user,
    journeyData, // Pass journey data for positional completion logic
  });

  // İlk yüklemede, eğer selectedLevel null ise kullanıcının gerçek seviyesini seç
  useEffect(() => {
    if (!selectedLevel && user?.journeyLevel?.name) {
      dispatch(setSelectedLevel(user.journeyLevel.name.toLowerCase()));
    }
  }, [user?.journeyLevel?.name, selectedLevel, dispatch]);

  // Level veya journey verisi değiştiğinde tıklanabilir kartı ortala
  useEffect(() => {
    if (!isInitialized && journeyData && trackingData) {
      const timer = setTimeout(() => {
        if (carouselRef.current?.swiper) {
          const clickableIndex = getFirstClickableCardIndex(journeyData);
          carouselRef.current.swiper.slideTo(clickableIndex);
          setIsInitialized(true);
        }
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [selectedLevel, journeyData, trackingData, isInitialized, getFirstClickableCardIndex]);

  // GET ACTIVE JOURNEY CARD
  useEffect(() => {
    if (trackingData && journeyData) {
      if (user?._id && user?.journeyLevel?.name == 'master') {
        //check active journey card by checking tracking data and journey daya
        const completedJourney = trackingData?.[selectedLevel?.toLowerCase()];

        //last item of completedJourney if it exists
        const lastCompletedJourneyCard = completedJourney?.[completedJourney?.length - 1];

        if (lastCompletedJourneyCard) {
          //find index of lastCompletedJourneyCard in journeyData
          const lastCompletedJourneyIndex = journeyData?.findIndex(
            (item) => item._id === lastCompletedJourneyCard
          );

          if (journeyData[lastCompletedJourneyIndex + 1]) {
            setActiveJourneyCard(journeyData[lastCompletedJourneyIndex + 1]);
          }
        }
      }
    }
  }, [user?._id, user?.journeyLevel?.name, journeyData, trackingData, selectedLevel]);

  // Check genai card is active - Yeni oluşturduğumuz processGenAICard fonksiyonunu kullan
  useEffect(() => {
    // Eğer işlem zaten devam ediyorsa veya kart yoksa çık
    if (genAICardProcessingRef.current || !activeJourneyCard) {
      return;
    }

    if (activeJourneyCard.journeyType === 'GenAI Playground') {
      genAICardProcessingRef.current = true;

      processGenAICard({
        userId: user?._id,
        trackingData,
        selectedLevel,
        activeJourneyCard,
        refetchTracking,
      })
        .then(() => {
          genAICardProcessingRef.current = false;
        })
        .catch(() => {
          genAICardProcessingRef.current = false;
        });
    }
  }, [activeJourneyCard, user?._id, trackingData, selectedLevel, refetchTracking]);

  // Auto-complete cards based on positional logic and update tracking data
  useEffect(() => {
    const autoUpdateTracking = async () => {
      if (!journeyData?.length || !trackingData || !selectedLevel || !user?._id) {
        return;
      }

      try {
        // Get updated tracking data with auto-completed cards
        const updatedTrackingData = updateTrackingWithAutoCompletedCards(
          journeyData,
          trackingData,
          selectedLevel
        );

        // Check if there are any changes to save
        const currentCompletedIds = trackingData[selectedLevel.toLowerCase()] || [];
        const newCompletedIds = updatedTrackingData[selectedLevel.toLowerCase()] || [];

        if (newCompletedIds.length > currentCompletedIds.length) {
          // Update tracking data in the backend
          await updateJourneyTracking({
            userId: user._id,
            journeyTracking: updatedTrackingData,
          }).unwrap();

          // Refresh tracking data
          if (refetchTracking) {
            await refetchTracking();
          }

          console.log('Auto-completed cards updated in tracking data');
        }
      } catch (error) {
        console.error('Failed to auto-update tracking data:', error);
      }
    };

    autoUpdateTracking();
  }, [journeyData, trackingData, selectedLevel, user?._id, updateJourneyTracking, refetchTracking]);

  // Go to next level if all cards are completed
  useEffect(() => {
    if (journeyData?.length > 0 && trackingData && selectedLevel && user?.journeyLevel?.name) {
      // Tüm kartlar tamamlandığında seviye yükseltme işlemini yap
      checkAndUpgradeLevel({
        journeyData,
        trackingData,
        selectedLevel,
        user,
        refetchJourneys,
        refetchTracking,
      });
    }
  }, [
    journeyData,
    trackingData,
    selectedLevel,
    user,
    refetchJourneys,
    refetchTracking,
    checkAndUpgradeLevel,
  ]);

  // Modal kapatma fonksiyonu
  const handleModalClose = async (result) => {
    await handleJourneyModalClose(result, refetchTracking, refetchJourneys, setIsInitialized);
  };

  const handleLevelChange = (event) => {
    dispatch(setSelectedLevel(event.target.value));
    setIsInitialized(false); // Level değiştiğinde initialized durumunu sıfırla
  };

  const handleSlideChange = () => {
    // Slide değişikliği izleme kodu buraya eklenebilir
  };

  const handleSkipJourney = () => {
    setOpenSkipJourneyConfirmDialog(true);
  };

  const handleCloseSkipJourneyConfirmDialog = () => {
    setOpenSkipJourneyConfirmDialog(false);
  };

  const confirmSkipJourney = async () => {
    setOpenSkipJourneyConfirmDialog(false);

    if (onSkipJourney) {
      await onSkipJourney();
    } else {
      // Varsayılan skip journey fonksiyonu
      try {
        const result = await skipToLevel(user?._id, 'expert');

        if (result.status === 'success') {
          dispatch(
            updateUser({
              ...user,
              journeyLevel: {
                name: 'expert',
                translations: {
                  en: 'Expert1',
                  de: 'Expert1e',
                },
                skippedJourney: ['beginner'],
              },
            })
          );

          dispatch(setSelectedLevel('expert'));
        } else {
          console.error('Journey level update failed:', result.error);
        }
      } catch (error) {
        console.error('Error skipping journey:', error);
      }
    }
  };

  // Eğer section gizlenmek isteniyorsa, boş döndür
  if (hideSection || !user) {
    return null;
  }

  return (
    <>
      {showUserProgress && (
        <Box mb={4}>
          <WelcomeHeader
            name={user.name}
            functionLabel={user?.onboarding?.function_label}
            jobRoleLabel={user?.onboarding?.job_role_label}
            showProgress={true}
            level={userActualLevel}
            progress={userActualProgress}
            completedSteps={userActualCompletedSteps}
            totalSteps={userActualTotalSteps}
          />
        </Box>
      )}
      <Box className="journey-section">
        <WhiteContainer
          title={containerTitle || t('home.trainingJourneys')}
          selectLevel={true}
          selectedLevel={selectedLevel}
          onLevelChange={handleLevelChange}
          variant="transparent"
          containerProps={{
            sx: {
              '@media (min-width: 768px)': {
                padding: 0,
              },
            },
          }}
          showNavigation={true}
        >
          <Carousel
            ref={carouselRef}
            onSlideChange={handleSlideChange}
            swiperProps={{
              slidesPerView: 3,
              spaceBetween: 24,
              initialSlide: getFirstClickableCardIndex(journeyData),
              centeredSlides: true,
              centeredSlidesBounds: true,
              slideToClickedSlide: true,
              watchSlidesProgress: true,
              loop: false,
              observer: true,
              observeParents: true,
              breakpoints: {
                320: { slidesPerView: Math.min(1, journeyData?.length || 1) },
                768: { slidesPerView: Math.min(2, journeyData?.length || 2) },
                1024: { slidesPerView: Math.min(3, journeyData?.length || 3) },
                1440: { slidesPerView: Math.min(4, journeyData?.length || 4) },
              },
            }}
          >
            {isJourneyLoading ? (
              <Typography>Loading...</Typography>
            ) : (
              journeyData?.map((item, index) => {
                const isCompleted = isJourneyCompleted(item._id);
                const previousCompleted = isPreviousCompleted(index, journeyData);
                const uniqueKey = `journey-${item._id}-${index}`;
                const translationKey = getTranslationKey(i18n.language, user?.onboarding?.language);
                const selectedTranslation =
                  item.translations?.[translationKey] || item.translations?.english || {};

                const getButtonText = () => {
                  if (isCompleted) return t('home.journeyCard.completed');
                  if (previousCompleted || user?.role?.toLowerCase() === 'administrator')
                    return t('home.journeyCard.playNow');
                  return t('home.journeyCard.locked');
                };

                return (
                  <JourneyCard
                    key={uniqueKey}
                    isLocked={
                      !isCompleted &&
                      !previousCompleted &&
                      user?.role?.toLowerCase() !== 'administrator' &&
                      !item.levels?.some((level) =>
                        user?.journeyLevel?.skippedJourney?.includes(level)
                      )
                    }
                    buttonText={getButtonText()}
                    buttonType={
                      item.buttonType?.toLowerCase() === 'selected'
                        ? 'Selected Content'
                        : item.buttonType?.toLowerCase() === 'url'
                          ? 'URL'
                          : item.buttonType?.toLowerCase() === 'file'
                            ? 'File'
                            : 'URL'
                    }
                    percent={isCompleted ? 100 : previousCompleted ? item.percent : 0}
                    onClick={() =>
                      (isCompleted ||
                        previousCompleted ||
                        user?.role?.toLowerCase() === 'administrator' ||
                        item.levels?.some((level) =>
                          user?.journeyLevel?.skippedJourney?.includes(level)
                        )) &&
                      handleCardClick(item, i18n.language)
                    }
                    title={selectedTranslation.title || item.title}
                  >
                    <CardContent
                      title={selectedTranslation.title || item.title}
                      description={selectedTranslation.description || item.description}
                      buttonURL={item.buttonURL || '#'}
                      newTab={item.newTab}
                      isLocked={
                        !isCompleted &&
                        !previousCompleted &&
                        user?.role?.toLowerCase() !== 'administrator'
                      }
                      isCompleted={isCompleted}
                      buttonText={getButtonText()}
                      hideButton={
                        !isCompleted &&
                        !previousCompleted &&
                        user?.role?.toLowerCase() !== 'administrator'
                      }
                      onClick={() =>
                        (isCompleted ||
                          previousCompleted ||
                          user?.role?.toLowerCase() === 'administrator') &&
                        handleCardClick(item, i18n.language)
                      }
                      journeyType={item.journeyType}
                      buttonType={
                        item.buttonType?.toLowerCase() === 'selected'
                          ? 'Selected Content'
                          : item.buttonType?.toLowerCase() === 'url'
                            ? 'URL'
                            : item.buttonType?.toLowerCase() === 'file'
                              ? 'File'
                              : 'URL'
                      }
                      content={selectedTranslation.content || item.content}
                    />
                  </JourneyCard>
                );
              })
            )}
          </Carousel>
          {showSkipJourney &&
            user?.onboarding?.function_label.slug === 'it-ai-data' &&
            (user?.onboarding?.ai_knowledge_label.slug === 'i-am-a-heavy-ai-user' ||
              user?.onboarding?.ai_knowledge_label.slug === 'i-am-an-ai-expert' ||
              user?.onboarding?.ai_knowledge_label.slug ===
                'i-am-actively-using-ai-on-a-regular-basis') &&
            user?.journeyLevel?.name === 'beginner' && (
              <>
                <Button
                  variant="link"
                  color="primary"
                  onClick={handleSkipJourney}
                  sx={{
                    float: 'right',
                    backgroundColor: 'transparent',
                    color: '#0072E5',
                    textTransform: 'none',
                  }}
                >
                  {t('home.skipJourney')}
                </Button>
                <SkipJourneyModal
                  openSkipJourneyConfirmDialog={openSkipJourneyConfirmDialog}
                  handleCloseSkipJourneyConfirmDialog={handleCloseSkipJourneyConfirmDialog}
                  confirmSkipJourney={confirmSkipJourney}
                  i18n={i18n}
                />
              </>
            )}
        </WhiteContainer>
      </Box>
      {console.log('modalContent:', modalContent)}
      {/* Normal içerik modalı */}
      <AIModal
        open={isModalOpen}
        onClose={handleModalClose}
        title={modalContent.title}
        content={modalContent.content}
        cardId={modalContent.cardId}
        isCompleted={modalContent.isCompleted}
        subtitles={modalContent.subtitles}
      />

      {/* Ideation Form Modal */}
      {isIdeationModalOpen && (
        <FormModal
          open={isIdeationModalOpen}
          onClose={handleIdeationModalClose}
          title={t('ideation.form.title', 'Share Your Idea')}
          onSubmit={() => handleIdeationFormSubmit(refetchTracking, refetchJourneys)}
          maxWidth="md"
          activeStep={activeStep}
          steps={[
            {
              title: t('ideation.form.steps.describe.title', 'Describe your idea'),
              label: t('ideation.form.steps.describe.label', 'Description'),
              onChange: handleFormChange,
              content: (
                <FormRender
                  hideTitle={true}
                  hideDescription={false}
                  formTitle={t('ideation.form.steps.describe.title', 'Describe your idea')}
                  formData={{
                    _id: ideationFormData?.data?._id || ideationFormData?._id || 'idea-form',
                    title:
                      ideationFormData?.data?.title ||
                      ideationFormData?.title ||
                      t('ideation.form.title', 'Share Your Idea'),
                    description:
                      ideationFormData?.data?.description || ideationFormData?.description || '',
                    type: 'form',
                    topics:
                      (ideationFormData?.data?.topics || ideationFormData?.topics || [])
                        .map((topic) => ({
                          ...topic,
                          fields:
                            topic.fields?.filter((field) => {
                              const name = field.name?.toLowerCase().trim();
                              return (
                                name === 'title' ||
                                name === 'description' ||
                                name.includes('describe') ||
                                name.includes('title')
                              );
                            }) || [],
                        }))
                        .filter((topic) => topic.fields?.length > 0) || [],
                    fields:
                      (ideationFormData?.data?.fields || ideationFormData?.fields || []).filter(
                        (field) => {
                          const name = field.name?.toLowerCase().trim();
                          return (
                            name === 'title' ||
                            name === 'description' ||
                            name.includes('describe') ||
                            name.includes('title')
                          );
                        }
                      ) || [],
                  }}
                  onChange={handleFormChange}
                  values={formData}
                  errors={formErrors}
                  disabled={isSubmittingForm}
                />
              ),
            },
            {
              title: t('ideation.form.steps.classify.title', 'Classify your idea'),
              label: t('ideation.form.steps.classify.label', 'Classification'),
              onChange: handleFormChange,
              content: (
                <FormRender
                  hideTitle={true}
                  hideDescription={true}
                  formTitle={t('ideation.form.steps.classify.title', 'Classify your idea')}
                  formData={{
                    _id: ideationFormData?.data?._id || ideationFormData?._id || 'idea-form',
                    title:
                      ideationFormData?.data?.title ||
                      ideationFormData?.title ||
                      t('ideation.form.title', 'Share Your Idea'),
                    description:
                      ideationFormData?.data?.description || ideationFormData?.description || '',
                    type: 'form',
                    topics:
                      (ideationFormData?.data?.topics || ideationFormData?.topics || [])
                        .map((topic) => ({
                          ...topic,
                          fields:
                            topic.fields?.filter((field) => {
                              const name = field.name?.toLowerCase().trim();
                              return (
                                name !== 'title' &&
                                name !== 'description' &&
                                !name.includes('describe') &&
                                !name.includes('title')
                              );
                            }) || [],
                        }))
                        .filter((topic) => topic.fields?.length > 0) || [],
                    fields:
                      (ideationFormData?.data?.fields || ideationFormData?.fields || []).filter(
                        (field) => {
                          const name = field.name?.toLowerCase().trim();
                          return (
                            name !== 'title' &&
                            name !== 'description' &&
                            !name.includes('describe') &&
                            !name.includes('title')
                          );
                        }
                      ) || [],
                  }}
                  onChange={handleFormChange}
                  values={formData}
                  errors={formErrors}
                  disabled={isSubmittingForm}
                />
              ),
            },
          ]}
          formErrors={formErrors}
          loading={isSubmittingForm}
          showReview={true}
          values={formData}
          onChange={handleFormChange}
        >
          {isIdeationFormLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Box sx={{ position: 'relative' }}>
              {isSubmittingForm && (
                <Box
                  sx={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: 'rgba(255, 255, 255, 0.7)',
                    zIndex: 2,
                  }}
                >
                  <Box sx={{ textAlign: 'center' }}>
                    <CircularProgress size={60} />
                    <Typography sx={{ mt: 2 }}>{t('common.loading', 'Loading...')}</Typography>
                  </Box>
                </Box>
              )}
            </Box>
          )}
        </FormModal>
      )}
    </>
  );
};

JourneySection.propTypes = {
  user: PropTypes.object.isRequired,
  showSkipJourney: PropTypes.bool,
  onSkipJourney: PropTypes.func,
  containerTitle: PropTypes.string,
  hideSection: PropTypes.bool,
  showUserProgress: PropTypes.bool,
  handleJourneyLevelUpdate: PropTypes.func,
};

export default JourneySection;
