// Ideation listesi i<PERSON><PERSON> k<PERSON>ılacak olan hook

import { useGetFormListQuery } from '@/redux/services/form-response';

const useIdeationList = (submittedBy, formType) => { 

    const { data, isLoading, error } = useGetFormListQuery({
        submittedBy,
        formType
    }); 
    const ideationList = data?.docs?.map((item) => ({
        id: item.id,
        title: item.title,
        status: item.status,
        description: item.description,
        responses: item.responses,
        createdAt: item.createdAt,
    })); 

    return { ideationList, isLoading, error };
};

export default useIdeationList;

