import React, { useRef } from 'react';
import PropTypes from 'prop-types';
import {
  Modal,
  Box,
  Typography,
  Grid,
  IconButton,
  Divider,
  List,
  ListItem,
  ListItemText,
  Paper,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import LightbulbIcon from '@mui/icons-material/Lightbulb';
import CardWithIcon from '@/components/CardWithIcon/CardWithIcon';
import { useTranslation } from 'react-i18next';
import { useState } from 'react';

const modalStyle = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: { xs: '90%', sm: '80%', md: '60%' },
  maxHeight: '90vh',
  bgcolor: 'background.paper',
  borderRadius: 2,
  boxShadow: 24,
  p: 4,
  overflow: 'auto',
  outline: 'none',
  border: 'none',
};

const SubmittedIdeaCard = ({ idea, formData }) => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const modalRef = useRef(null); 
  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false); 
  // Utility function to detect field types
  const detectFieldType = (fieldName) => {
    const lowerName = fieldName.toLowerCase();
    
    // Title detection
    if (
      lowerName.includes('title') || 
      lowerName.includes('title of your idea')
    ) {
      return 'title';
    }
    
    // Description detection with multiple patterns
    if (
      lowerName.includes('description') ||
      lowerName.includes('describe your idea')
    ) {
      return 'description';
    }
    
    return 'other';
  };

  // Backdrop tıklamasını kontrol etmek için
  const handleBackdropClick = (event) => {
    if (event.target === event.currentTarget) {
      handleClose();
    }
  };

  // Filter idea title and description
  // using the utility function to detect field types
  const getIdeaTitle = () => {
    const titleResponse = idea.responses.find((response) =>
      detectFieldType(response.name) === 'title'
    );
    return titleResponse?.value || 'Untitled idea';
  };

  const getIdeaDescription = () => {
    const descriptionResponse = idea.responses.find((response) =>
      detectFieldType(response.name) === 'description'
    );
    return descriptionResponse?.value || 'No description provided';
  };

  const filteredIdeaTitle = getIdeaTitle();
  const filteredIdeaDescription = getIdeaDescription();

  return (
    <Grid item xs={12} md={6}>
      <CardWithIcon
        icon={<LightbulbIcon sx={{ width: 24, height: 24 }} />}
        title={filteredIdeaTitle}
        description={filteredIdeaDescription}
        variant="sales"
        onClick={handleOpen}
        buttonText={t('cockpit.submittedIdeas.viewDetails')}
        buttonType="FUNCTION"
        buttonFunction={handleOpen}
        newTab={false}
        date={idea.createdAt}
        lineClamp={3}
      />

      <Modal
        open={open}
        onClose={handleClose}
        aria-labelledby="modal-title"
        aria-describedby="modal-description"
        disableEscapeKeyDown={false}
        onClick={handleBackdropClick} 
        keepMounted
        disableEnforceFocus={false}
        disableAutoFocus={false}
        ref={modalRef}
      >
        <Box
          sx={modalStyle}
          onClick={(e) => e.stopPropagation()}
          role="dialog"
          aria-modal="true"
          tabIndex={-1}
        >
          {/* Modal Header */}
          <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
            <Typography
              id="modal-title"
              variant="h6"
              component="h2"
              sx={{
                wordWrap: 'break-word',
                wordBreak: 'break-word',
                hyphens: 'auto',
                lineHeight: 1.3,
                flex: 1,
                marginRight: 2,
              }}
            >
              {filteredIdeaTitle}
            </Typography>
            <IconButton
              aria-label="close modal"
              onClick={handleClose}
              sx={{ color: 'text.secondary' }}
              tabIndex={0}
            >
              <CloseIcon />
            </IconButton>
          </Box>

          <Divider sx={{ mb: 2 }} />

          {/* Modal Content */}
          <Box id="modal-description">
            {/* Description Section */}
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
              {t('cockpit.submittedIdeas.description', 'Description')}
            </Typography>
            <Typography
              variant="body1"
              paragraph
              sx={{
                wordWrap: 'break-word',
                wordBreak: 'break-word',
                hyphens: 'auto',
                lineHeight: 1.5,
              }}
            >
              {filteredIdeaDescription}
            </Typography>

            {/* Responses Section */}
            <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                {t('cockpit.submittedIdeas.responses', 'Responses')}
            </Typography>
            <Paper variant="outlined" sx={{ mt: 2 }}>
              <List>
                
                {idea.responses.map((response, index) => {
                 
                  let fieldId = response.fieldId;
                  let fieldValue = response.value;
                  let fieldName = response.name;
                  let matchingField = null;

                  // Search through formData (topics) to find matching field
                  formData?.topics?.forEach(topic => {
                    topic.fields?.forEach(field => {
                      if (field._id === fieldId) {
                        matchingField = field;
                      }
                    });
                  }); 
                  if (matchingField?.label) {
                    fieldName = matchingField.label;
                  }
                  // If matching field has options, find the matching option and use its label
                  if (matchingField?.options?.length > 0) {
                    const matchingOption = matchingField.options.find(
                      option => option.value === response.value
                    );
                    if (matchingOption) {
                      fieldValue = matchingOption.label;
                    }
                  }

                  if (  (typeof response.value === 'number' || !isNaN(Number(response.value))) && Number(response.value) <= 0) {
                      return null;
                  }
                  return (
                    <React.Fragment key={index}>
                      <ListItem>
                        <ListItemText
                          primary={
                            <Typography
                              variant="subtitle2"
                              color="text.primary"
                              sx={{
                              wordWrap: 'break-word',
                              wordBreak: 'break-word',
                              hyphens: 'auto',
                              lineHeight: 1.4,
                              textTransform: 'capitalize',
                            }}
                          >
                            {fieldName}
                          </Typography>
                        }
                        secondary={
                          <Typography
                            variant="body2"
                            color="text.secondary"
                            sx={{
                              wordWrap: 'break-word',
                              wordBreak: 'break-word',
                              hyphens: 'auto',
                              lineHeight: 1.5,
                            }}
                          >
                            {fieldValue}
                          </Typography>
                        }
                      />
                    </ListItem>
                    {index < idea.responses.length - 1 && <Divider />}
                  </React.Fragment>
                );
                })}
              </List>
            </Paper>

            {/* Status and Date Section */}
            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between' }}>
              {/* <Typography variant="body2" color="text.secondary">
                Status: {idea.status}
              </Typography> */}
              <Typography variant="body2" color="text.secondary"></Typography>
              <Typography variant="body2" color="text.secondary">
                {t('cockpit.submittedIdeas.created')}{' '}
                {(() => {
                  const dateObj = new Date(idea.createdAt);
                  const day = String(dateObj.getUTCDate()).padStart(2, '0');
                  const month = String(dateObj.getUTCMonth() + 1).padStart(2, '0');
                  const year = dateObj.getUTCFullYear();
                  const hours = String(dateObj.getUTCHours()).padStart(2, '0');
                  const minutes = String(dateObj.getUTCMinutes()).padStart(2, '0');
                  return `${day}/${month}/${year} - ${hours}:${minutes}`;
                })()}
              </Typography>
            </Box>
          </Box>
        </Box>
      </Modal>
    </Grid>
  );
};

SubmittedIdeaCard.propTypes = {
  idea: PropTypes.shape({
    id: PropTypes.string,
    title: PropTypes.string.isRequired,
    description: PropTypes.string.isRequired,
    status: PropTypes.string,
    responses: PropTypes.arrayOf(
      PropTypes.shape({
        name: PropTypes.string.isRequired,
        value: PropTypes.string.isRequired,
      })
    ).isRequired,
    createdAt: PropTypes.string,
  }).isRequired,
};

export default SubmittedIdeaCard;
