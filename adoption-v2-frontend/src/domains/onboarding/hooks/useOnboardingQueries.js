import {
  useAiExperienceQuery,
  useFunctionLabelsQuery,
  useIndustryQuery,
  useJobRolesQuery,
  useManagementRolesQuery,
  usePlatformLanguagesQuery,
  useTechnicalBackgroundQuery,
  useTermsQuery,
} from '../../../redux/services/cds-api';

// Import StepTypes from the parent component
const StepTypes = {
  welcome: 0,
  changePassword: 1,
  language: 2,
  businessFunction: 3,
  jobRole: 4,
  managementRole: 5,
  technicalBackground: 6,
  aiExperience: 7,
  industry: 8,
  terms: 9,
  review: 10,
  mandatoryVideo: 11,
};

export const useOnboardingQueries = (activeStep, formData) => {
  const queries = {
    languages: usePlatformLanguagesQuery(undefined, {
      skip: activeStep !== StepTypes.language,
    }),

    functions: useFunctionLabelsQuery(undefined, {
      skip: activeStep !== StepTypes.businessFunction,
    }),

    jobRoles: useJobRolesQuery(
      typeof formData.function_label === 'object'
        ? formData.function_label.slug
        : formData.function_label,
      {
        skip: activeStep !== StepTypes.jobRole || !formData.function_label,
      }
    ),

    managementRoles: useManagementRolesQuery(undefined, {
      skip: activeStep !== StepTypes.managementRole,
    }),

    technicalBackgrounds: useTechnicalBackgroundQuery(undefined, {
      skip: activeStep !== StepTypes.technicalBackground,
    }),

    aiExperience: useAiExperienceQuery(undefined, {
      skip: activeStep !== StepTypes.aiExperience,
    }),

    industry: useIndustryQuery(undefined, {
      skip: activeStep !== StepTypes.industry,
    }),
    terms: useTermsQuery(undefined, {
      skip: activeStep !== StepTypes.terms,
    }),
  };

  return {
    data: {
      languages: queries.languages.data?.data,
      functions: queries.functions.data?.data,
      jobRoles: queries.jobRoles.data?.data,
      managementRoles: queries.managementRoles.data?.data,
      technicalBackgrounds: queries.technicalBackgrounds.data?.data,
      aiExperience: queries.aiExperience.data?.data,
      industry: queries.industry.data?.data,
      terms: queries.terms.data?.data,
    },
    isLoading: {
      languages: queries.languages.isLoading,
      functions: queries.functions.isLoading,
      jobRoles: queries.jobRoles.isLoading,
      managementRoles: queries.managementRoles.isLoading,
      technicalBackgrounds: queries.technicalBackgrounds.isLoading,
      aiExperience: queries.aiExperience.isLoading,
      industry: queries.industry.isLoading,
      terms: queries.terms.isLoading,
    },
  };
};
