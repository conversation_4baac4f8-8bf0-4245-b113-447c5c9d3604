import { useLocation, Navigate, Outlet } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { selectCurrentToken, logOut } from '../redux/features/auth/authSlice';
import { useState, useEffect, useCallback } from 'react';
import { Box, CircularProgress } from '@mui/material';
import Onboarding from '../domains/onboarding';
import { jwtDecode } from 'jwt-decode';
import { useMaintenanceMode } from './MaintenanceModeProvider';
import MaintenancePage from '../pages/Maintenance';
import axios from 'axios';

const AuthProvider = () => {
  const token = useSelector(selectCurrentToken);
  const user = useSelector((state) => state.auth.user);
  const location = useLocation();
  const dispatch = useDispatch();
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [userData, setUserData] = useState(null);

  // Get maintenance mode context
  const { shouldShowMaintenance, isLoading: isMaintenanceLoading } = useMaintenanceMode();

  // Public routes that don't require authentication
  const publicPaths = ['/login'];
  const isPublicPath = publicPaths.includes(location.pathname);

  // Special pages where onboarding won't be shown
  const specialPages = ['/terms-and-conditions', '/privacy-policy'];
  const isSpecialPage = specialPages.includes(location.pathname);

  // Function to check token validity
  const isTokenExpired = useCallback((token) => {
    if (!token) return true;

    try {
      const decodedToken = jwtDecode(token);
      const currentTime = Date.now() / 1000;

      // Return true if token has expired
      return decodedToken.exp < currentTime;
    } catch (error) {
      console.error('Token decode error:', error);
      return true;
    }
  }, []);

  // Setup Axios interceptor
  useEffect(() => {
    const requestInterceptor = axios.interceptors.request.use(
      (config) => {
        if (token && !isTokenExpired(token)) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    const responseInterceptor = axios.interceptors.response.use(
      (response) => {
        return response;
      },
      (error) => {
        // Session expire check
        if (error.response?.status === 401) {
          const errorData = error.response.data;

          // If SESSION_EXPIRED error comes from backend
          if (
            errorData?.data === 'SESSION_EXPIRED' ||
            errorData?.message?.includes('Session expired') ||
            errorData?.message?.includes('logged out from another location')
          ) {
            console.warn('Session expired or user logged out from another location');

            // Logout user
            dispatch(logOut());

            // Redirect to login page (this component will re-render so Navigate will work automatically)
            return Promise.reject(new Error('Session expired'));
          }
        }
        return Promise.reject(error);
      }
    );

    // Cleanup function
    return () => {
      axios.interceptors.request.eject(requestInterceptor);
      axios.interceptors.response.eject(responseInterceptor);
    };
  }, [token, isTokenExpired, dispatch]);

  // Token check and logout if necessary
  const checkTokenAndLogout = useCallback(() => {
    if (token && isTokenExpired(token) && !isPublicPath) {
      dispatch(logOut());
    }
  }, [token, isTokenExpired, isPublicPath, dispatch]);

  // Check token when page loads and when token changes
  useEffect(() => {
    checkTokenAndLogout();
  }, [checkTokenAndLogout]);

  // Periodically check token (every 5 minutes)
  useEffect(() => {
    if (token && !isPublicPath) {
      const tokenCheckInterval = setInterval(
        () => {
          checkTokenAndLogout();
        },
        5 * 60 * 1000
      ); // 5 minutes

      return () => clearInterval(tokenCheckInterval);
    }
  }, [token, isPublicPath, checkTokenAndLogout]);

  useEffect(() => {
    if (token && user && !user.onboarding && !isPublicPath && !isSpecialPage) {
      setUserData({
        ...user,
        onboarding: null,
      });
      setShowOnboarding(true);
    }
  }, [token, user, isPublicPath, isSpecialPage]);

  // Show loading while maintenance mode is being checked
  if (isMaintenanceLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <CircularProgress size={60} />
      </Box>
    );
  }

  // Check maintenance mode first - if maintenance is active and user can't bypass, show maintenance page
  if (shouldShowMaintenance && !isPublicPath) {
    return <MaintenancePage />;
  }

  if ((!token || isTokenExpired(token)) && !isPublicPath) {
    // Redirect to login if not authenticated and trying to access protected route
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  if (token && !isTokenExpired(token) && isPublicPath) {
    // If user is logged in and there's a redirect in state, go there
    const redirectTo = location.state?.from?.pathname || '/';
    return <Navigate to={redirectTo} replace />;
  }

  // If the route is public or user is authenticated, render the route
  return (
    <>
      <Outlet />
      <Onboarding open={showOnboarding} userData={userData} />
    </>
  );
};

export default AuthProvider;
