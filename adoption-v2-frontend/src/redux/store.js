import { configureStore } from '@reduxjs/toolkit';
import {
  persistStore,
  persistReducer,
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
} from 'redux-persist';
import { autoMergeLevel2 } from 'redux-persist/lib/stateReconciler/autoMergeLevel2';
import storage from 'redux-persist/lib/storage';
import authReducer from './features/auth/authSlice';
import settingsReducer from './features/settings/settingsSlice';
import { authApi } from './services/auth-api';
import { cdsApi } from './services/cds-api';
import { platformSettingsApi } from './services/platform-settings-api';
import { userApi } from './services/user-api';
import { journeyApi, journeyTrackingApi } from './services/journey-api';
import { useCaseApi } from './services/use-case-api';
import { workflowApi } from './services/workflow-api';
import { ssoServerApi } from './services/sso-server.api';
import { createWorkflowApi } from './services/create-workflow';
import { connectorsApi } from './services/connectors-api';
import { appTrackingApi } from './services/app-tracking-api';
import { coursesApi } from './services/courses-api';
import { promptLibraryApi } from './services/prompt-library-api';
import { createAppApi } from './services/CreateApp-api';
import trainingReducer from './features/training/trainingSlice';
import ssoServerReducer from './features/sso-server/sso-serverSlice';
import coursesReducer from './features/courses/courseSlice';
import cdsReducer from './features/cds/cdsSlice';
import { quizProgressApi } from './services/quiz-progress-api';
import { cockpitApi } from './services/cockpit-api';
import { formApi } from './services/form-service';
import { formResponseApi } from './services/form-response';
import { quizApi } from './services/quiz-service';
import { appShareApi } from './services/appshare-api';

const authPersistConfig = {
  key: 'root',
  version: 1,
  storage,
  whitelist: ['user', 'token'],
  debug: true,
  stateReconciler: autoMergeLevel2,
};

const trainingPersistConfig = { key: 'training', storage, whitelist: ['selectedLevel'] };

const coursesPersistConfig = {
  key: 'courses',
  storage,
  whitelist: [
    'currentCardId',
    'currentCourse',
    'currentProgress',
    'isCourseCompleted',
    'progressPercentage',
  ],
};

const persistedAuthReducer = persistReducer(authPersistConfig, authReducer);
const persistedTrainingReducer = persistReducer(trainingPersistConfig, trainingReducer);
const persistedCoursesReducer = persistReducer(coursesPersistConfig, coursesReducer);

const rootReducer = {
  auth: persistedAuthReducer,
  training: persistedTrainingReducer,
  courses: persistedCoursesReducer,
  settings: settingsReducer,
  cds: cdsReducer,
  ssoServer: ssoServerReducer,
  [authApi.reducerPath]: authApi.reducer,
  [cdsApi.reducerPath]: cdsApi.reducer,
  [platformSettingsApi.reducerPath]: platformSettingsApi.reducer,
  [userApi.reducerPath]: userApi.reducer,
  [journeyApi.reducerPath]: journeyApi.reducer,
  [journeyTrackingApi.reducerPath]: journeyTrackingApi.reducer,
  [useCaseApi.reducerPath]: useCaseApi.reducer,
  [workflowApi.reducerPath]: workflowApi.reducer,
  [ssoServerApi.reducerPath]: ssoServerApi.reducer,
  [createWorkflowApi.reducerPath]: createWorkflowApi.reducer,
  [coursesApi.reducerPath]: coursesApi.reducer,
  [connectorsApi.reducerPath]: connectorsApi.reducer,
  [appTrackingApi.reducerPath]: appTrackingApi.reducer,
  [quizProgressApi.reducerPath]: quizProgressApi.reducer,
  [promptLibraryApi.reducerPath]: promptLibraryApi.reducer,
  [createAppApi.reducerPath]: createAppApi.reducer,
  [cockpitApi.reducerPath]: cockpitApi.reducer,
  [formApi.reducerPath]: formApi.reducer,
  [formResponseApi.reducerPath]: formResponseApi.reducer,
  [quizApi.reducerPath]: quizApi.reducer,
  [appShareApi.reducerPath]: appShareApi.reducer,
};

export const store = configureStore({
  reducer: rootReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: { ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER] },
    })
      .concat(authApi.middleware)
      .concat(cdsApi.middleware)
      .concat(platformSettingsApi.middleware)
      .concat(userApi.middleware)
      .concat(journeyApi.middleware)
      .concat(journeyTrackingApi.middleware)
      .concat(useCaseApi.middleware)
      .concat(workflowApi.middleware)
      .concat(ssoServerApi.middleware)
      .concat(createWorkflowApi.middleware)
      .concat(coursesApi.middleware)
      .concat(connectorsApi.middleware)
      .concat(appTrackingApi.middleware)
      .concat(quizProgressApi.middleware)
      .concat(promptLibraryApi.middleware)
      .concat(createAppApi.middleware)
      .concat(cockpitApi.middleware)
      .concat(formApi.middleware)
      .concat(quizApi.middleware)
      .concat(formResponseApi.middleware)
      .concat(appShareApi.middleware),
});

export const persistor = persistStore(store);
