import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { CDS_API_URL, CDS_API_KEY } from '../../config-global';

// Constants
const CACHE_TIME = 600; // 10 minutes
const PREFETCH_TIME = 60; // 1 minute

export const quizApi = createApi({
  reducerPath: 'quizApi',
  baseQuery: fetchBaseQuery({
    baseUrl: CDS_API_URL,
    prepareHeaders: (headers, { getState }) => {
      const token = getState().auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      headers.set('x-api-key', CDS_API_KEY);
      return headers;
    },
  }),
  endpoints: (builder) => ({
    quizzes: builder.query({
      query: (params) => {
        // Eğer quizId varsa tekil quiz getir
        if (params?.quizId) {
          return {
            url: `/quizzes/${params.quizId}`,
            method: 'GET',
            params: { lang: params?.language },
          };
        }
        // Tüm quizleri getir
        return {
          url: '/quizzes',
          method: 'GET',
          params: { lang: params?.language },
        };
      },
      transformResponse: (response) => {
        if (response?.data?.quiz) {
          return response.data.quiz;
        } else if (response?.data) {
          return response.data;
        }
        return null;
      },
      keepUnusedDataFor: CACHE_TIME,
    }),
  }),
});

// Prefetching functions
export const prefetchQuiz = (quizId) => {
  return quizApi.util.prefetch('quizzes', quizId, { force: false, ifOlderThan: PREFETCH_TIME });
};

export const { useQuizzesQuery } = quizApi;
