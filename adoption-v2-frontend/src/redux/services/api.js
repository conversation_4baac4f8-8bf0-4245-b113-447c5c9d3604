import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { setCredentials, logOut } from '../features/auth/authSlice';
import { API_URL } from '../../config-global';

// Request timeout value
const REQUEST_TIMEOUT = 15000; // 15 seconds

const baseQuery = fetchBaseQuery({
  baseUrl: API_URL,
  credentials: 'include',
  timeout: REQUEST_TIMEOUT, // Limiting hanging requests with timeout
  prepareHeaders: (headers, { getState }) => {
    const token = getState().auth.token;
    if (token) {
      headers.set('Authorization', `Bearer ${token}`);
    }

    // Optimizing browser caching by adding cache control headers
    headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    headers.set('Pragma', 'no-cache');
    headers.set('Expires', '0');

    return headers;
  },
});

const baseQueryWithReauth = async (args, api, extraOptions) => {
  let result = await baseQuery(args, api, extraOptions);

  // Session expire check on 401 (Unauthorized) error
  if (result.error?.status === 401 || result.error?.originalStatus === 401) {

    // Check if session has expired
    const errorData = result.error?.data;
    const isSessionExpired =
      errorData?.data === 'SESSION_EXPIRED' ||
      errorData?.message?.includes('Session expired') ||
      errorData?.message?.includes('logged out from another location') ||
      errorData?.message?.includes('Invalid Token') ||
      errorData?.message?.includes('No Token Provided');

    if (isSessionExpired) {
      api.dispatch(logOut());
    } else {
      // In session-based auth, 401 = logout
      api.dispatch(logOut());
    }
  }

  return result;
};

export const api = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryWithReauth,
  // Setting cache behavior globally
  keepUnusedDataFor: 120, // 2 minutes (in seconds)
  refetchOnMountOrArgChange: false, // Disable automatic query on component mount
  refetchOnFocus: false, // Disable re-query when page gets focus
  refetchOnReconnect: false, // Disable query on reconnect
  tagTypes: ['Shortcuts', 'UserShortcuts', 'Shortcut', 'User'],
  endpoints: () => ({}),
});
