import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { API_URL, CDS_API_URL, CDS_API_KEY } from '../../config-global';

// Journey tracking için ayrı bir API tanımlıyoruz

export const journeyTrackingApi = createApi({
  reducerPath: 'journeyTrackingApi',
  baseQuery: fetchBaseQuery({
    baseUrl: API_URL,
    prepareHeaders: (headers, { getState }) => {
      const token = getState().auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  endpoints: (builder) => ({
    getJourneyTracking: builder.query({
      query: (userId) => {
        if (!userId) {
          return { url: 'tracking/journey/error' }; // Geçici bir URL, isteği engellemek için
        }
        return { url: `tracking/journey/${userId}` };
      },
      transformResponse: (response) => {
        if (!response || !response.data) {
          return { beginner: [], expert: [], master: [], seenModals: [] };
        }
        return response.data;
      },
      // Her seferinde yeni veri almayı zorla
      forceRefetch: ({ currentArg, previousArg }) => {
        return currentArg !== previousArg;
      },
    }),
    updateJourneyTracking: builder.mutation({
      query: ({ userId, journeyTracking }) => ({
        url: `tracking/journey/${userId}`,
        method: 'PATCH',
        body: { journeyTracking },
      }),
      // Başarılı güncelleme sonrası getJourneyTracking'i yeniden çağır
      invalidatesTags: ['JourneyTracking'],
    }),
    updateSeenModals: builder.mutation({
      query: ({ userId, modalKey }) => ({
        url: `tracking/journey/modals/${userId}`,
        method: 'PATCH',
        body: { modalKey },
      }),
      // Başarılı güncelleme sonrası getJourneyTracking'i yeniden çağır
      invalidatesTags: ['JourneyTracking'],
    }),
  }),
  tagTypes: ['JourneyTracking'],
});

// Journey için ana API
export const journeyApi = createApi({
  reducerPath: 'journeyApi',
  baseQuery: fetchBaseQuery({
    baseUrl: CDS_API_URL,
    prepareHeaders: (headers, { getState }) => {
      const token = getState().auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      headers.set('x-api-key', CDS_API_KEY);
      return headers;
    },
  }),

  endpoints: (builder) => ({
    getJourneys: builder.query({
      query: (params) => {
        return {
          url: 'journey/get',
          method: 'GET',
          params: {
            function:
              typeof params?.function === 'object'
                ? params.function.slug
                : params?.function || 'sales',
            levels: params?.levels,
            provider: params?.provider || 'ai-business-school',
            managementRole: params?.managementRole,
          },
        };
      },
      transformResponse: (response) => {
        if (!response || !response.data) {
          return [];
        }
        return response.data;
      },
    }),
  }),
});

export const { useGetJourneysQuery } = journeyApi;
export const {
  useGetJourneyTrackingQuery,
  useUpdateJourneyTrackingMutation,
  useUpdateSeenModalsMutation,
} = journeyTrackingApi;
