import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { API_URL } from '../../config-global';

export const coursesApi = createApi({
  reducerPath: 'coursesApi',
  baseQuery: fetchBaseQuery({
    baseUrl: API_URL,
    prepareHeaders: (headers, { getState }) => {
      const token = getState().auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  endpoints: (builder) => ({
    checkCertificate: builder.query({
      query: ({ userId, courseId }) => ({
        url: '/certificates/check',
        method: 'GET',
        params: { userId, courseId },
      }),
    }),
    generateCertificate: builder.mutation({
      query: (certificateData) => ({
        url: '/certificates/generate',
        method: 'POST',
        body: certificateData,
      }),
    }),
    downloadCertificate: builder.query({
      query: (fileName) => ({
        url: `/certificates/download/${fileName}`,
        method: 'GET',
        responseHandler: (response) => response.blob(),
      }),
    }),
    getCertificatesByUserId: builder.query({
      query: (userId) => ({
        url: `/certificates/list?userId=${userId}`,
        method: 'GET',
      }),
    }),

    getCompletedCoursesByUserId: builder.query({
      query: (userId) => ({
        url: `/tracking/course/${userId}/?status=completed`,
        method: 'GET',
      }),
    }),
    getInProgressCoursesByUserId: builder.query({
      query: (userId) => ({
        url: `/tracking/course/${userId}/?status=progress`,
        method: 'GET',
      }),
    }),

    updateProgress: builder.mutation({
      query: ({ courseId, progressData }) => ({
        url: `/tracking/course/courses/${courseId}/tracking`,
        method: 'POST',
        body: {
          ...progressData,
          userId: progressData.userId || undefined,
          isVideoProgress: false,
        },
      }),
    }),
  }),
});

export const {
  useCheckCertificateQuery,
  useGenerateCertificateMutation,
  useDownloadCertificateQuery,
  useGetCertificatesByUserIdQuery,
  useGetCompletedCoursesByUserIdQuery,
  useGetInProgressCoursesByUserIdQuery,
  useUpdateProgressMutation,
} = coursesApi;
