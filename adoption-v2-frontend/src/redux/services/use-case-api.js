import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { CDS_API_URL, CDS_API_KEY } from '../../config-global';

export const useCaseApi = createApi({
  reducerPath: 'useCaseApi',
  baseQuery: fetchBaseQuery({
    baseUrl: CDS_API_URL,
    prepareHeaders: (headers, { getState }) => {
      const token = getState().auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      headers.set('x-api-key', CDS_API_KEY);
      return headers;
    },
  }),
  endpoints: (builder) => ({
    getUseCases: builder.query({
      query: (params) => {
        const queryParams = {};
        if (params?.limit) {
          queryParams.limit = params.limit;
        }
        if (params?.function_label) {
          queryParams.function_label =
            typeof params.function_label === 'object'
              ? params.function_label.slug
              : params.function_label;
        }
        return {
          url: 'usecase',
          method: 'GET',
          params: queryParams,
        };
      },
      transformResponse: (response) => {
        if (!response || !response.data) {
          return [];
        }
        return response.data.useCases;
      },
    }),
    getUseCaseById: builder.query({
      query: (id) => ({
        url: `usecase/${id}`,
        method: 'GET',
      }),
      transformResponse: (response) => {
        if (!response || !response.data) {
          return null;
        }
        return response.data;
      },
    }),
    getUseCaseBySlug: builder.query({
      query: (slug) => ({
        url: `usecase/slug/${slug}`,
        method: 'GET',
      }),
      transformResponse: (response) => {
        if (!response || !response.data) {
          return null;
        }
        return response.data;
      },
    }),
    getUsecasesListByIds: builder.query({
      query: (usecaseIds) => ({
        url: `/usecase/list/`,
        method: 'POST',
        body: usecaseIds,
      }),
    }),
  }),
});

export const {
  useGetUseCasesQuery,
  useGetUseCaseBySlugQuery,
  useGetUsecasesListByIdsQuery,
  useGetUseCaseByIdQuery,
} = useCaseApi;
