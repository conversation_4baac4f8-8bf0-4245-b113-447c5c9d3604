import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { API_URL } from '../../config-global';

// Form yanıtları için API servisi
export const formResponseApi = createApi({
  reducerPath: 'formResponseApi',
  baseQuery: fetchBaseQuery({
    baseUrl: API_URL,
    prepareHeaders: (headers, { getState }) => {
      const token = getState().auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['FormResponses'],
  endpoints: (builder) => ({
    // Form yanıtlarını gönder
    submitFormResponse: builder.mutation({
      query: (formData) => ({
        url: '/form-responses',
        method: 'POST',
        body: formData,
      }),
      invalidatesTags: ['FormResponses'],
      transformResponse: (response) => {
        // <PERSON><PERSON><PERSON><PERSON> dönüştür
        if (response) {
          return {
            status: 'success',
            data: response.data || response,
            message: 'Form successfully submitted',
          };
        }
        return response;
      },
      transformErrorResponse: (response) => {
        // Hata mesajını yapılandır
        return {
          status: 'error',
          message: response.data?.message || response.error || 'Form submission failed',
          error: response.data || response,
        };
      },
    }),

    getFormList: builder.query({
      query: (params) => {
        const { submittedBy, formType } = params;
        return `/form-responses/?submittedBy=${submittedBy}&formType=${formType}`;
      },
      providesTags: ['FormResponses'],
      refetchOnMountOrArgChange: true,
      refetchOnFocus: true,
      refetchOnReconnect: true,
      transformResponse: (response) => {
        if (response?.data) {
          return response.data;
        }
        return response;
      },
    }),

  }),
});

// Manual file upload function
export const uploadFile = async (formData, token = null) => {
  // Token'ı parametre olarak al, yoksa localStorage'dan al
  const authToken = token || localStorage.getItem('token');

  const response = await fetch(`${API_URL}/form-responses/upload-file`, {
    method: 'POST',
    body: formData,
    headers: {
      'Authorization': `Bearer ${authToken}`,
    },
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || 'File upload failed');
  }

  const result = await response.json();
  return result.data;
};

// Hook'ları export et
export const { useSubmitFormResponseMutation, useGetFormListQuery } = formResponseApi;
