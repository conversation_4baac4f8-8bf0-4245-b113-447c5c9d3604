import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { API_URL, CDS_API_URL, CDS_API_KEY } from '../../config-global';

// Define dynamic baseFetchQuery
const getBaseQuery = (url) => {
  return fetchBaseQuery({
    baseUrl: url,
    prepareHeaders: (headers, { getState }) => {
      const token = getState().auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      headers.set('x-api-key', CDS_API_KEY);
      return headers;
    },
  });
};

export const createWorkflowApi = createApi({
  reducerPath: 'createWorkflowApi',
  baseQuery: getBaseQuery(API_URL), // Use API_URL by default
  endpoints: (builder) => ({
    // Primary API endpoint - uses API_URL
    getWorkflowById: builder.query({
      query: (params) => {
        // Accept simple string or object parameter
        const slug = typeof params === 'object' ? params.slug : params;
        // Create correct URL for main API
        return {
          url: `workflow-creator/slug/${slug}`,
          method: 'GET',
        };
      },
      transformResponse: (response) => {
        if (!response || !response.data) {
          return null;
        }

        return response;
      },
      // Evaluate API response and switch to other API if necessary
      async onQueryStarted(params, { dispatch, queryFulfilled }) {
        try {
          // Wait for first API request result
          const result = await queryFulfilled;

          // If no data or no userId, make request to CDS_API
          if (!result.data || !result.data.data || !result.data.data.userId) {
            // Get slug
            const slug = typeof params === 'object' ? params.slug : params;

            // Make request to CDS API
            dispatch(createWorkflowApi.endpoints.getWorkflowByCdsApi.initiate(slug));
          }
        } catch (error) {
          console.error('API error, trying CDS_API_URL:', error);
          // Make request to CDS API in case of error
          const slug = typeof params === 'object' ? params.slug : params;
          dispatch(createWorkflowApi.endpoints.getWorkflowByCdsApi.initiate(slug));
        }
      },
    }),

    // Secondary API endpoint - uses CDS_API_URL
    getWorkflowByCdsApi: builder.query({
      queryFn: async (slug, api, extraOptions) => {
        // Create custom baseQuery for CDS API
        const customBaseQuery = getBaseQuery(CDS_API_URL);

        try {
          // Send request to CDS API
          const result = await customBaseQuery(
            {
              url: `workflow/slug/${slug}`,
              method: 'GET',
            },
            api,
            extraOptions
          );

          if (result.error) {
            throw new Error(result.error?.data?.message || 'CDS API error');
          }

          // Manually add successful response to getWorkflowById cache
          // So UI can see this
          if (result.data) {
            try {
              // Update getWorkflowById cache
              // So data will be displayed in UI
              api.dispatch(
                createWorkflowApi.util.upsertQueryData('getWorkflowById', { slug }, result.data)
              );
            } catch (cacheError) {
              console.error('Cache update error:', cacheError);
            }
          }

          // Return result
          return { data: result.data };
        } catch (error) {
          console.error('CDS API error:', error);
          return {
            error: {
              data: {
                message: error.message || 'CDS API request failed',
              },
            },
          };
        }
      },
    }),

    getWorkflowsByUser: builder.query({
      query: (userId) => ({
        url: `/workflow-creator/`,
        method: 'GET',
        params: {
          userId: userId,
          limit: 30,
          page: 1,
        },
      }),
      transformResponse: (response) => {
        if (response?.data && Array.isArray(response.data)) {
          return response.data;
        } else if (response?.data?.workflows && Array.isArray(response.data.workflows)) {
          return response.data.workflows;
        } else if (response?.results && Array.isArray(response.results)) {
          return response.results;
        } else if (Array.isArray(response)) {
          return response;
        }
        return [];
      },
      transformErrorResponse: (response) => {
        return {
          status: response.status,
          message: response.data?.message || 'User workflows could not be retrieved',
        };
      },
    }),

    saveAndRunWorkflow: builder.mutation({
      query: (workflowData) => {
        // If workflowData has an ID, it's an update operation
        if (workflowData.id) {
          return {
            url: `workflow-creator/${workflowData.id}`,
            method: 'PATCH',
            body: workflowData,
          };
        }

        // Otherwise, it's a create operation
        return {
          url: 'workflow-creator',
          method: 'POST',
          body: workflowData,
        };
      },
      transformResponse: (response) => {
        if (!response) {
          throw new Error('No response received');
        }
        return response;
      },
      transformErrorResponse: (response) => {
        return response;
      },
    }),
  }),
});

export const {
  useGetWorkflowByIdQuery,
  useGetWorkflowByCdsApiQuery,
  useSaveAndRunWorkflowMutation,
  useGetWorkflowsByUserQuery,
} = createWorkflowApi;
