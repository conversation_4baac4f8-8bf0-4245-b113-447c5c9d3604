import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { API_URL } from '../../config-global';
import { toast } from 'react-toastify';
import i18n from '../../i18n';

// Utility function to check usage limits and show warning
const checkUsageLimitAndShowWarning = (errorData) => {
  // Handle different error response structures using optional chaining
  const usageData = errorData?.data?.data ?? errorData?.data ?? errorData;

  // Check if usageData has the required properties and usage limit is exceeded
  const { currentUsage, limit } = usageData || {};
  if (currentUsage !== undefined && limit !== undefined && currentUsage >= limit) {
    // Show usage limit warning
    toast.error(i18n.t('common.errors.usageLimitExceeded'), {
      position: 'top-right',
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      toastId: 'usage-limit-warning', // Prevent duplicate toasts
    });
    return true;
  }
  return false;
};

// Journey tracking için ayrı bir API tanımlıyoruz

export const connectorsApi = createApi({
  reducerPath: 'connectorsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: API_URL,
    prepareHeaders: (headers, { getState }) => {
      const token = getState().auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  endpoints: (builder) => ({
    assistantConnectors: builder.mutation({
      async queryFn(data, _queryApi) {
        try {
          const response = await fetch(`${API_URL}/connectors/text-generation`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${_queryApi.getState().auth.token}`,
            },
            body: JSON.stringify({
              name: 'assistant',
              prompt: data.prompt,
              temperature: data.temperature || 1,
              frequency: data.frequency || 0,
              presence: data.presence || 0,
              assistantId: data.assistantId,
              instructions: data.instructions || data.assistant_settings?.assistant_instructions,
              assistant_settings: data.assistant_settings,
              stream: true,
            }),
            signal: data.abortController?.signal,
          });

          if (!response.ok) {
            const errorData = await response.json();
            // Check usage limits for text-generation
            checkUsageLimitAndShowWarning(errorData);
            throw new Error(errorData.message || 'API response failed');
          }

          const result = await response.json();

          // onUpdate callback'ini çağır
          if (data.onUpdate && result.data?.content) {
            data.onUpdate(
              JSON.stringify({
                data: {
                  content: result.data.content,
                },
              })
            );
          }

          return { data: result.data?.content || result.data };
        } catch (error) {
          return {
            error: {
              status: error.status || 500,
              data: error.message || 'An error occurred',
            },
          };
        }
      },
    }),
    useAzureOpenAIConnectors: builder.mutation({
      async queryFn(data, _queryApi) {
        try {
          const response = await fetch(`${API_URL}/connectors/text-generation`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${_queryApi.getState().auth.token}`,
            },
            body: JSON.stringify({
              name: 'OpenAI',
              prompt: data.prompt,
              temperature: data.temperature,
              frequency: data.frequency,
              presence: data.presence,
              topp: data.topP,
              stream: data.stream !== false,
              html: data.html === true,
            }),
            signal: data.abortController?.signal,
          });

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            // Check usage limits for text-generation
            checkUsageLimitAndShowWarning(errorData);
            throw new Error(`API response failed: ${response.status} ${response.statusText}`);
          }

          // Stream modu kapalıysa doğrudan JSON yanıtını işle
          if (data.stream === false) {
            const jsonResult = await response.json();

            // API yanıtını işle
            if (jsonResult && jsonResult.data && jsonResult.data.content && data.onUpdate) {
              // İçeriği tek seferde gönder, html ve streaming değerlerini de ekle
              data.onUpdate(
                JSON.stringify({
                  data: {
                    content: jsonResult.data.content,
                    html: jsonResult.data.html,
                    streaming: jsonResult.data.streaming,
                  },
                })
              );
            }

            return {
              data: jsonResult.data || jsonResult,
            };
          }

          // Stream modu açıksa stream yanıtını işle
          if (!response.body) {
            throw new Error('Response body not found');
          }

          const reader = response.body.getReader();
          const decoder = new TextDecoder();

          try {
            while (true) {
              const { done, value } = await reader.read();

              if (done) {
                break;
              }

              const chunk = decoder.decode(value);
              const lines = chunk.toString().split('\n');

              for (const line of lines) {
                const trimmedLine = line.trim();
                if (!trimmedLine || !trimmedLine.startsWith('data: ')) continue;

                const jsonData = trimmedLine.slice(5).trim();
                if (jsonData === '[DONE]') {
                  continue;
                }

                try {
                  const parsed = JSON.parse(jsonData);
                  if (parsed.content && data.onUpdate) {
                    data.onUpdate(trimmedLine);
                  }
                } catch {
                  // JSON ayrıştırma hatası olduğunda, veriyi güvenli şekilde formatla
                  if (jsonData && data.onUpdate) {
                    try {
                      // Özel karakterlerin düzgün escape edildiğinden emin ol
                      const escapedContent = JSON.stringify(jsonData);
                      data.onUpdate(`data: {"content": ${escapedContent}}`);
                    } catch {
                      // Son çare: düz metin olarak gönder
                      data.onUpdate(`data: {"content": "Processing content..."}`);
                    }
                  }
                }
              }
            }

            return { data: 'Stream completed' };
          } catch {
            throw new Error('Response cancelled');
          }
        } catch (error) {
          return {
            error: {
              status: error.status || 500,
              data: error.message || 'An error occurred',
            },
          };
        }
      },
    }),
    useDallEConnectors: builder.mutation({
      async queryFn(data, _queryApi, _, fetchWithBQ) {
        try {
          const response = await fetchWithBQ({
            url: 'connectors/image-generation',
            method: 'POST',
            body: {
              name: 'dall-e',
              prompt: data.prompt,
              temperature: data.temperature || 1,
              frequency: data.frequency || 0,
              presence: data.presence || 0,
              aspectRatio: data.aspectRatio || '1:1',
            },
          });

          // Check for usage limit errors in image-generation
          if (response.error) {
            const isUsageLimitExceeded = checkUsageLimitAndShowWarning(response.error);
            if (isUsageLimitExceeded) {
              // Modify error to indicate usage limit was exceeded
              return {
                ...response,
                error: {
                  ...response.error,
                  isUsageLimitExceeded: true,
                  data: 'Usage limit exceeded',
                },
              };
            }
          }

          return response;
        } catch (error) {
          return {
            error: {
              status: error.status || 500,
              data: error.message || 'An error occurred',
            },
          };
        }
      },
    }),
    heygenVideoCreateConnectors: builder.mutation({
      async queryFn(data, _queryApi, _, fetchWithBQ) {
        try {
          const response = await fetchWithBQ({
            url: 'connectors/video-generation',
            method: 'POST',
            body: {
              name: 'heygen',
              prompt: data.prompt,
              assistantId: data.assistantId || '',
              temperature: data.temperature || 1,
              frequency: data.frequency || 0,
              presence: data.presence || 0,
              avatar_id: data.avatar_id,
              voice_id: data.voice_id,
            },
          });

          // Check for usage limit errors in video-generation
          if (response.error) {
            const isUsageLimitExceeded = checkUsageLimitAndShowWarning(response.error);
            if (isUsageLimitExceeded) {
              // Modify error to indicate usage limit was exceeded
              return {
                ...response,
                error: {
                  ...response.error,
                  isUsageLimitExceeded: true,
                  data: 'Usage limit exceeded',
                },
              };
            }
          }

          return response;
        } catch (error) {
          return {
            error: {
              status: error.status || 500,
              data: error.message || 'An error occurred',
            },
          };
        }
      },
    }),
    heygenAvatarConnectors: builder.query({
      query: (params) => ({
        url: 'connectors/heygen/avatars/local',
        method: 'GET',
        params: {
          limit: params?.limit || 10,
          page: params?.page || 1,
        },
      }),
      serializeQueryArgs: ({ endpointName }) => {
        return endpointName;
      },
      merge: (currentCache, newItems) => {
        if (!currentCache || newItems?.data?.pagination?.currentPage === 1) {
          return newItems;
        }

        if (!newItems?.data?.avatars?.length) {
          return currentCache;
        }

        return {
          ...newItems,
          data: {
            ...newItems.data,
            avatars: [...currentCache.data.avatars, ...newItems.data.avatars],
            pagination: {
              ...newItems.data.pagination,
              currentPage: newItems.data.pagination.currentPage,
            },
          },
        };
      },
      forceRefetch: ({ currentArg, previousArg }) => {
        return currentArg?.page !== previousArg?.page || currentArg?.limit !== previousArg?.limit;
      },
    }),
    heygenVoiceConnectors: builder.query({
      query: (params) => ({
        url: 'connectors/heygen/voices',
        method: 'GET',
        params: {
          limit: params?.limit || 10,
          page: params?.page || 1,
        },
      }),
      serializeQueryArgs: ({ endpointName }) => {
        return endpointName;
      },
      merge: (currentCache, newItems) => {
        if (!currentCache) return newItems;
        if (newItems?.data?.length === 0) return currentCache;

        return {
          ...newItems,
          data: [...currentCache.data, ...newItems.data],
        };
      },
      forceRefetch: ({ currentArg, previousArg }) => {
        return currentArg?.page !== previousArg?.page;
      },
    }),
    heygenVideoStatusConnectors: builder.query({
      query: (params) => ({
        url: `connectors/heygen/video/${params?.video_id}`,
        method: 'GET',
      }),
      serializeQueryArgs: ({ endpointName }) => {
        return endpointName;
      },
      forceRefetch: ({ currentArg, previousArg }) => {
        return currentArg?.video_id !== previousArg?.video_id;
      },
    }),
  }),
});

export const {
  useUseAzureOpenAIConnectorsMutation,
  useUseDallEConnectorsMutation,
  useGetConnectorsQuery,
  useHeygenVideoCreateConnectorsMutation,
  useHeygenAvatarConnectorsQuery,
  useHeygenVoiceConnectorsQuery,
  useHeygenVideoStatusConnectorsQuery,
  useAssistantConnectorsMutation,
} = connectorsApi;
