import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { CDS_API_URL } from '../../config-global';

export const promptLibraryApi = createApi({
  reducerPath: 'promptLibraryApi',
  baseQuery: fetchBaseQuery({
    baseUrl: CDS_API_URL,
    prepareHeaders: (headers, { getState }) => {
      const token = getState().auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      headers.set('x-api-key', import.meta.env.VITE_CDS_API_KEY);
      return headers;
    },
  }),
  endpoints: (builder) => ({
    getPrompts: builder.query({
      query: (params) => {
        const queryParams = {
          page: params?.page || 1,
          limit: params?.limit || 9,
          provider: params?.provider !== 'all' ? params?.provider : undefined,
          product: params?.app !== 'all' ? params?.app : undefined,
        };

        if (params?.function_label && params.function_label !== 'all') {
          queryParams.function_label = params.function_label;
        }

        if (params?.language) {
          queryParams.language = params.language;
        }

        return {
          url: '/promptlibrary/',
          method: 'GET',
          params: queryParams
        };
      },
      transformResponse: (response) => {
        if (!response || !response.data) {
          return {
            prompts: [],
            pagination: {
              total: 0,
              page: 1,
              totalPages: 1,
              hasNextPage: false,
              hasPrevPage: false,
              limit: 9
            }
          };
        }
        return {
          prompts: response.data.prompts || [],
          pagination: response.data.pagination || {
            total: 0,
            page: 1,
            totalPages: 1,
            hasNextPage: false,
            hasPrevPage: false,
            limit: 9
          }
        };
      },
      transformErrorResponse: (response) => {
        return response.data?.message || 'An error occurred while fetching prompts';
      }
    }),

    getPromptById: builder.query({
      query: (id) => ({
        url: `/promptlibrary/${id}`,
        method: 'GET'
      }),
      transformResponse: (response) => {
        if (response?.data) {
          return response.data;
        }
        throw new Error('Failed to fetch prompt details');
      },
      transformErrorResponse: (response) => {
        return response.data?.message || 'An error occurred while fetching prompt details';
      }
    })
  })
});

export const {
  useGetPromptsQuery,
  useGetPromptByIdQuery
} = promptLibraryApi; 