import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { cdsApi } from '../../services/cds-api';
import { API_URL, CDS_API_URL, CDS_API_KEY } from '../../../config-global';
import { useSelector } from 'react-redux';

// CDS API'ye course endpoints'lerini ekliyoruz
export const courseApi = cdsApi.injectEndpoints({
  endpoints: (builder) => ({
    fetchCourses: builder.query({
      query: () => ({ url: 'courses/get', method: 'GET' }),
      transformResponse: (response) => {
        if (response?.status !== 'success' || !response?.data?.courses) {
          throw new Error('Invalid data structure received from API');
        }
        return response.data.courses;
      },
    }),
    fetchCourseDetails: builder.query({
      query: (courseId) => ({ url: `courses/${courseId}`, method: 'GET' }),

      transformResponse: (response) => {
        const course = response.data;
        if (!course || !course.title) {
          throw new Error('Course data is missing required fields');
        }
        return course;
      },
    }),

    updateProgress: builder.mutation({
      query: ({ courseId, progressData }) => ({
        url: `courses/${courseId}/progress`,
        method: 'PATCH',
        body: progressData,
      }),
    }),
    submitQuiz: builder.mutation({
      query: ({ quizId, answers }) => ({
        url: `quizzes/${quizId}/submit`,
        method: 'POST',
        body: { answers },
      }),
    }),
  }),
});

// Environment variables
// Artık doğrudan import edilen değişkenleri kullanıyoruz
// const CDS_API_URL = CONFIG.cdsApiUrl;
// const ADOPTION_API_URL = CONFIG.apiUrl;

// Async thunks
export const fetchCourseDetails = createAsyncThunk(
  'course/fetchCourseDetails',
  async (courseId, { rejectWithValue, dispatch }) => {
    try {
      dispatch(clearCurrentCourse());

      const response = await fetch(`${CDS_API_URL}/courses/${courseId}`, {
        headers: {
          'x-api-key': CDS_API_KEY,
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        let errorMessage;
        try {
          // Hata yanıtını text olarak almaya çalış
          errorMessage = await response.text();
        } catch (textError) {
          // Response body zaten okunmuşsa veya başka bir hata varsa
          errorMessage = `Yanıt metni okunamadı: ${textError.message}`;
        }
        console.error('API error response:', errorMessage);
        return rejectWithValue(`API Hatası: ${response.status} - ${errorMessage}`);
      }

      // Response'u güvenli bir şekilde JSON olarak parse et
      let data;
      try {
        data = await response.json();
      } catch (jsonError) {
        console.error('JSON parse error:', jsonError);
        return rejectWithValue(`JSON ayrıştırma hatası: ${jsonError.message}`);
      }

      if (!data.data) {
        console.error('Missing data.data in API response:', data);
        return rejectWithValue('Kurs verisi alınamadı');
      }

      return data.data;
    } catch (error) {
      console.error('Kurs detayları alınırken hata:', error);
      return rejectWithValue(error.message || 'Kurs detayları alınamadı');
    }
  }
);

export const fetchProgress = createAsyncThunk(
  'courses/fetchProgress',
  async (courseId, { getState, rejectWithValue }) => {
    try {
      const state = getState();
      const token = state.auth.user?.token;
      const userId = state.auth.user?._id;

      if (!token || !userId) {
        return rejectWithValue('Oturum bulunamadı');
      }

      // Eğer zaten progress verisi varsa, tekrar fetch etme
      const currentProgress = state.courses.currentProgress;
      if (currentProgress) {
        return currentProgress;
      }

      const response = await fetch(
        `${API_URL}/tracking/course/courses/${courseId}/tracking?userId=${userId}`,
        {
          method: 'GET',
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
          credentials: 'include',
        }
      );

      if (!response.ok) {
        let errorMessage;
        try {
          // Hata yanıtını text olarak almaya çalış
          errorMessage = await response.text();
        } catch (textError) {
          // Response body zaten okunmuşsa veya başka bir hata varsa
          errorMessage = `Yanıt metni okunamadı: ${textError.message}`;
        }
        console.error('API error response:', errorMessage);
        return rejectWithValue(`API Hatası: ${response.status} - ${errorMessage}`);
      }

      // Response'u güvenli bir şekilde JSON olarak parse et
      let data;
      try {
        data = await response.json();
      } catch (jsonError) {
        console.error('JSON parse error:', jsonError);
        return rejectWithValue(`JSON ayrıştırma hatası: ${jsonError.message}`);
      }

      if (!data || !data.data) {
        console.error('Missing data in progress API response:', data);
        return rejectWithValue('İlerleme verisi alınamadı');
      }

      return data.data;
    } catch (error) {
      console.error('İlerleme bilgisi alınırken hata:', error);
      return rejectWithValue(error.message || 'İlerleme bilgisi alınamadı');
    }
  },
  {
    condition: (_, { getState }) => {
      const { courses } = getState();
      // İstek zaten devam ediyorsa yeni istek yapma
      if (courses.loading) {
        return false;
      }
    },
  }
);

export const updateProgress = createAsyncThunk(
  'course/updateProgress',
  async ({ courseId, progressData }, { getState, rejectWithValue }) => {
    try {
      const state = getState();
      const token = state.auth.user?.token;
      const userId = state.auth.user?._id;

      if (!token || !userId) {
        return rejectWithValue('Oturum bulunamadı');
      }

      const response = await fetch(`${API_URL}/tracking/course/courses/${courseId}/tracking`, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          userId,
          chapterId: progressData.chapterId,
          topicId: progressData.topicId,
          completed: progressData.completed,
        }),
        credentials: 'include',
      });

      if (!response.ok) {
        let errorMessage;
        try {
          // Hata yanıtını text olarak almaya çalış
          errorMessage = await response.text();
        } catch (textError) {
          // Response body zaten okunmuşsa veya başka bir hata varsa
          errorMessage = `Yanıt metni okunamadı: ${textError.message}`;
        }
        console.error('Progress update API error:', errorMessage);
        return rejectWithValue(`API Hatası: ${response.status} - ${errorMessage}`);
      }

      // Response'u güvenli bir şekilde JSON olarak parse et
      let data;
      try {
        data = await response.json();
      } catch (jsonError) {
        console.error('Progress update JSON parse error:', jsonError);
        return rejectWithValue(`JSON ayrıştırma hatası: ${jsonError.message}`);
      }

      if (!data || !data.data) {
        console.error('Missing data in progress update API response:', data);
        return rejectWithValue('İlerleme güncellemesi verisi alınamadı');
      }

      return data.data;
    } catch (error) {
      console.error('İlerleme güncellenirken hata:', error);
      return rejectWithValue(error.message || 'İlerleme güncellenemedi');
    }
  }
);

const initialState = {
  courses: [],
  currentCourse: null,
  currentProgress: null,
  quizResult: null,
  loading: false,
  error: null,
  progressPercentage: 0,
  isCourseCompleted: false,
  currentCardId: null,
};

const courseSlice = createSlice({
  name: 'courses',
  initialState,
  reducers: {
    setCourses: (state, action) => {
      state.courses = action.payload;
      state.error = null;
    },
    setCurrentCourse: (state, action) => {
      state.currentCourse = action.payload;
      state.error = null;
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    clearCurrentCourse: (state) => {
      state.currentCourse = null;
    },
    clearQuizResult: (state) => {
      state.quizResult = null;
    },
    updateProgressPercentage: (state) => {
      if (!state.currentCourse || !state.currentProgress) return;

      // 1. Topic sayılarını topla
      const completedTopicsCount =
        state.currentProgress.chapters?.reduce((acc, chapter) => {
          return acc + (chapter.topics?.filter((topic) => topic.completed)?.length || 0);
        }, 0) || 0;

      const totalTopics =
        state.currentCourse?.chapters?.reduce(
          (acc, chapter) => acc + (chapter.topics?.length || 0),
          0
        ) || 0;

      // 2. İlerleme yüzdesini hesapla
      const calculatedPercentage =
        totalTopics > 0 ? Math.round((completedTopicsCount / totalTopics) * 100) : 0;

      state.progressPercentage = Math.min(calculatedPercentage, 100);

      // 3. Backend'den gelen kurs tamamlanma durumunu kontrol et
      const courseStatus = String(state.currentProgress?.courseComplateStatus || '').toLowerCase();
      const backendCompletedStatus = courseStatus === 'completed';

      // 4. Gerçek tamamlanma durumunu belirle
      // Kurs tamamlanmış sayılması için hem backend'in "completed" demesi
      // hem de tüm topic'lerin tamamlanmış olması gerekiyor
      const isCourseCompleted =
        backendCompletedStatus && totalTopics > 0 && completedTopicsCount === totalTopics;

      state.isCourseCompleted = isCourseCompleted;

      // 6. Chapter bazında detaylı loglama yapın
      let completedTopics = 0;
      let totalCourseTopics = 0;

      // Tüm chapter'ları döngüye alarak sayımları yap
      state.currentProgress?.chapters?.forEach((chapter) => {
        if (chapter.topics && chapter.topics.length > 0) {
          const chapterTotalTopics = chapter.topics.length;
          const chapterCompletedTopics = chapter.topics.filter((t) => t.completed).length;

          totalCourseTopics += chapterTotalTopics;
          completedTopics += chapterCompletedTopics;
        }
      });
    },
    setCurrentCardId: (state, action) => {
      state.currentCardId = action.payload;
    },
    clearCurrentCardId: (state) => {
      state.currentCardId = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Kurs detayları
      .addCase(fetchCourseDetails.pending, (state) => {
        state.loading = true;
        state.error = null;
        // Ensure we don't display stale data while fetching
        state.currentCourse = null;
      })
      .addCase(fetchCourseDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.currentCourse = action.payload;

        // Reset progress percentage when course changes
        if (
          state.currentProgress &&
          state.currentCourse &&
          state.currentProgress.courseId !== state.currentCourse._id
        ) {
          state.currentProgress = null;
          state.progressPercentage = 0;
        }
      })
      .addCase(fetchCourseDetails.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // İlerleme güncelleme
      .addCase(updateProgress.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateProgress.fulfilled, (state, action) => {
        state.loading = false;
        state.currentProgress = action.payload;
        // Progress güncellendiğinde yüzdeyi de güncelle
        courseSlice.caseReducers.updateProgressPercentage(state);
      })
      .addCase(updateProgress.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      .addCase(fetchProgress.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProgress.fulfilled, (state, action) => {
        state.loading = false;
        state.currentProgress = action.payload;
        // Progress fetch edildiğinde yüzdeyi de güncelle
        courseSlice.caseReducers.updateProgressPercentage(state);
      })
      .addCase(fetchProgress.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      });
  },
});

// Actions
export const {
  setCourses,
  setCurrentCourse,
  setLoading,
  setError,
  clearError,
  clearCurrentCourse,
  clearQuizResult,
  setCurrentCardId,
  clearCurrentCardId,
} = courseSlice.actions;

// Hooks
export const {
  useFetchCoursesQuery,
  useFetchCourseDetailsQuery,
  useUpdateProgressMutation,
  useSubmitQuizMutation,
} = courseApi;

// Selectors
export const selectCourses = (state) => state.courses.courses;
export const selectCurrentCourse = (state) => state.courses.currentCourse;
export const selectLoading = (state) => state.courses.loading;
export const selectError = (state) => state.courses.error;
export const selectQuizResult = (state) => state.courses.quizResult;
export const selectProgressPercentage = (state) => state.courses.progressPercentage;
export const selectIsCourseCompleted = (state) => state.courses.isCourseCompleted;
export const selectCurrentCardId = (state) => state.courses.currentCardId;

export default courseSlice.reducer;
