import React, { useRef, useState, useEffect, useMemo } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  useLoginMutation,
  useForgotPasswordMutation,
  useResetPasswordMutation,
} from '../../redux/services/auth-api';
import { platformSettingsApi } from '../../redux/services/platform-settings-api';
import { useDispatch } from 'react-redux';
import { setCredentials } from '../../redux/features/auth/authSlice';
import {
  setGeneralSettings,
  setMaintenanceMode,
} from '../../redux/features/settings/settingsSlice';
import { useForm } from 'react-hook-form';
import { Box } from '@mui/material';
import Loading from '../../components/Loading/Loading';
import Onboarding from '../../domains/onboarding';
import { useTranslation } from 'react-i18next';
import { PublicClientApplication } from '@azure/msal-browser';
import { SSO_MSAL_CONFIG } from '../../config-global';
import { useGetPublicSettingsQuery } from '../../redux/services/platform-settings-api';

// Components
import LoginForm from './components/LoginForm';
import ForgotPasswordForm from './components/ForgotPasswordForm';
import OTPForm from './components/OTPForm';
import PromoSection from './components/PromoSection';
import { developmentLogs } from '../../utils/developmentLogs';
import { extractUserNameFromMsal } from '../../utils/msalUtils';
// Styles
import './styles.scss';

const FORGOT_STEPS = {
  LOGIN: 'login',
  EMAIL: 'email',
  OTP: 'otp',
  NEW_PASSWORD: 'new_password',
};

const LoginPage = () => {
  const errRef = useRef();
  const [errMsg, setErrMsg] = useState(null);
  const [currentStep, setCurrentStep] = useState(FORGOT_STEPS.LOGIN);
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [userData, setUserData] = useState(null);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const otpRefs = useRef([...Array(6)].map(() => React.createRef()));
  const [forgotPasswordEmail, setForgotPasswordEmail] = useState('');
  const [resendDisabled, setResendDisabled] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);
  const [rememberMe, setRememberMe] = useState(false);
  const [isSsoLoading, setIsSsoLoading] = useState(false);
  const { i18n } = useTranslation();

  // Get public settings for defaultRole
  const { data: publicSettings } = useGetPublicSettingsQuery();

  // MSAL configuration
  const msalConfig = useMemo(
    () => ({
      auth: {
        clientId: SSO_MSAL_CONFIG.clientId,
        authority: `https://login.microsoftonline.com/${SSO_MSAL_CONFIG.tenantId}`,
        redirectUri: SSO_MSAL_CONFIG.redirectUri,
      },
      cache: {
        cacheLocation: 'sessionStorage',
        storeAuthStateInCookie: false,
      },
    }),
    []
  );

  // State for MSAL client
  const [msalInstance, setMsalInstance] = useState(null);
  const [msalInitialized, setMsalInitialized] = useState(false);

  // Initialize MSAL client
  useEffect(() => {
    const initializeMsal = async () => {
      try {
        const instance = new PublicClientApplication(msalConfig);
        await instance.initialize();
        setMsalInstance(instance);
        setMsalInitialized(true);
      } catch (error) {
        console.error('MSAL initialization error:', error);
        setErrMsg('Microsoft authentication service initialization failed');
      }
    };

    initializeMsal();
  }, [msalConfig, setErrMsg]);

  // Request configuration for SSO
  const loginRequest = {
    scopes: SSO_MSAL_CONFIG.scopes,
  };

  // SSO login function
  const handleSsoLogin = async () => {
    try {
      setIsSsoLoading(true);

      // Show error if MSAL is not initialized
      if (!msalInstance || !msalInitialized) {
        console.error('MSAL not initialized yet');
        setErrMsg('Microsoft authentication service is not ready yet. Please try again.');
        setIsSsoLoading(false);
        return;
      }

      const loginResponse = await msalInstance.loginPopup(loginRequest);

      if (loginResponse && loginResponse.account) {
        // SSO login successful, send token to backend and get user information
        // This part may change according to backend integration
        // For now we're using normal login process
        const email = loginResponse.account.username.toLowerCase(); // Convert to lowercase for case-insensitive matching

        // Extract name and surname information from Microsoft account using utility function
        const { name, surname } = extractUserNameFromMsal(loginResponse);

        // Notify backend that SSO login was performed
        // Send only email and ssoLogin information
        // Send name and surname information only for use when creating new user
        // as optional data
        const loginData = {
          email,
          ssoLogin: true,
        };

        // Add name and surname information only for use when creating new user
        // This information will only be used on backend side when creating new user
        if (name) loginData.name = name;
        if (surname) loginData.surname = surname;

        // Add defaultRole from platform settings for new user creation
        if (publicSettings?.data?.defaultRole) {
          loginData.defaultRole = publicSettings.data.defaultRole;
        }

        // Add defaultCompany from platform settings for new user creation
        if (publicSettings?.data?.defaultCompany) {
          loginData.defaultCompany = publicSettings.data.defaultCompany;
        }

        const response = await login(loginData).unwrap();

        if (!response || !response.data) {
          setErrMsg(
            "We couldn't log you in. Please make sure your email and password are correct."
          );
          setIsSsoLoading(false);
          return;
        }

        setUserData(response.data);

        dispatch(
          setCredentials({
            user: response.data,
            token: response.data.token,
          })
        );

        // Fetch platform settings after user logs in
        await fetchGeneralSettings();

        if (response.data.onboarding?.language) {
          localStorage.removeItem('i18nextLng');
          i18n.changeLanguage(response.data.onboarding.language);
          localStorage.setItem('i18nextLng', response.data.onboarding.language);
        }

        if (!response.data.onboarding) {
          setShowOnboarding(true);
        } else {
          const redirectTo = location.state?.from?.pathname || '/';
          navigate(redirectTo, { replace: true });
        }
      }
    } catch (error) {
      console.error('SSO Login Error:', error);

      // Check if it's a 403 error (restricted user creation)
      if (error.status === 403 || error.data?.status === 'error') {
        setErrMsg('Please contact your company for permission to create your account.');
      } else {
        setErrMsg(error.message || 'SSO login failed');
      }
    } finally {
      setIsSsoLoading(false);
    }
  };

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    formState: { isSubmitting },
    trigger,
    reset,
  } = useForm({
    mode: 'onSubmit',
  });

  const navigate = useNavigate();
  const location = useLocation();

  const [login, { isLoading }] = useLoginMutation();
  const [forgotPassword, { isLoading: isForgotLoading }] = useForgotPasswordMutation();
  const [resetPassword, { isLoading: isResetLoading }] = useResetPasswordMutation();
  const dispatch = useDispatch();

  // To fetch platform settings after user logs in
  const fetchGeneralSettings = async () => {
    try {
      const response = await dispatch(
        platformSettingsApi.endpoints.getGeneralSettings.initiate()
      ).unwrap();

      // Get data from API response
      if (response?.status === 'success' && response?.data) {
        // Save data object coming directly from API as general settings
        dispatch(setGeneralSettings(response.data));
        // Process maintenance mode data separately
        const maintenanceModeData = {
          isActive: response.data?.maintenanceMode || false,
          allowedUsers: response.data?.maintenanceModeAllowedUsers || [],
          allowedRoles: response.data?.maintenanceModeAllowedRoles || [],
        };
        dispatch(setMaintenanceMode(maintenanceModeData));
      } else {
        // If response is not as expected, save entire response
        dispatch(setGeneralSettings(response));

        // Default values for maintenance mode
        dispatch(
          setMaintenanceMode({
            isActive: false,
            allowedUsers: [],
            allowedRoles: [],
          })
        );
      }
    } catch (error) {
      developmentLogs('Error occurred while loading platform settings:', error);

      // Default values for maintenance mode in case of error
      dispatch(
        setMaintenanceMode({
          isActive: false,
          allowedUsers: [],
          allowedRoles: [],
        })
      );
    }
  };

  const onSubmit = async (data) => {
    if (isSubmitting) return;
    try {
      if (currentStep === FORGOT_STEPS.LOGIN) {
        const response = await login(data).unwrap();
        if (!response || !response.data) {
          setErrMsg(
            "We couldn't log you in. Please make sure your email and password are correct."
          );
          return;
        }
        setUserData(response.data);

        dispatch(
          setCredentials({
            user: response.data,
            token: response.data.token,
          })
        );

        // Fetch platform settings after user logs in
        await fetchGeneralSettings();

        if (response.data.onboarding?.language) {
          localStorage.removeItem('i18nextLng');
          i18n.changeLanguage(response.data.onboarding.language);
          localStorage.setItem('i18nextLng', response.data.onboarding.language);
        }

        if (!response.data.onboarding) {
          setShowOnboarding(true);
        } else {
          const redirectTo = location.state?.from?.pathname || '/';
          navigate(redirectTo, { replace: true });
        }
      } else if (currentStep === FORGOT_STEPS.EMAIL) {
        const response = await forgotPassword(data.email).unwrap();

        if (response.status === 'Error' && response.data === 'Email not found') {
          setErrMsg(
            "We couldn't log you in. Please make sure your email and password are correct."
          );
          return;
        }

        if (response.status === 'Error' && response.data?.includes('duplicate key error')) {
          setForgotPasswordEmail(data.email);
          setCurrentStep(FORGOT_STEPS.OTP);
          reset({});
          return;
        }

        if (response.status === 'success') {
          setForgotPasswordEmail(data.email);
          setCurrentStep(FORGOT_STEPS.OTP);
          reset({});
        }
      } else if (currentStep === FORGOT_STEPS.OTP) {
        // Get and check OTP values
        const otpValues = Array(6)
          .fill()
          .map((_, i) => data[`otp_digit_${i + 1}`] || '');

        // Create a string from OTP values
        const otp = otpValues.join('');

        // Check for 6-digit number
        if (otp.length !== 6 || !/^\d{6}$/.test(otp)) {
          setErrMsg('Please enter a valid 6-digit verification code');
          return;
        }

        const resetData = {
          email: forgotPasswordEmail,
          otp,
          password: data.newPassword,
        };

        const response = await resetPassword(resetData).unwrap();
        if (response.status === 'Success') {
          const loginResponse = await login({
            email: forgotPasswordEmail,
            password: data.newPassword,
          }).unwrap();
          dispatch(
            setCredentials({
              user: loginResponse.data,
              token: loginResponse.data.token,
            })
          );

          // Fetch platform settings when logged in after password reset
          await fetchGeneralSettings();

          const redirectTo = location.state?.from?.pathname || '/';
          navigate(redirectTo, { replace: true });
        }
      }
    } catch (err) {
      console.error('Error:', err);
      if (err.data?.message === 'OTP not found or expired') {
        setErrMsg('Verification code is invalid or has expired. Please try again.');
      } else if (
        err.status === 403 ||
        (err.data?.status === 'error' && err.data?.message?.includes('restricted'))
      ) {
        setErrMsg(
          'Please contact your company to create your account. SSO user creation is currently restricted.'
        );
      } else if (!err.status) {
        setErrMsg("We couldn't log you in. Please make sure your email and password are correct.");
      } else if (err.status === 400) {
        setErrMsg('Please fill in all required fields');
      } else {
        setErrMsg("We couldn't log you in. Please make sure your email and password are correct.");
      }
      errRef.current?.focus();
    }
  };

  const handleOtpPaste = (e) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').trim();
    if (/^\d{6}$/.test(pastedData)) {
      [...pastedData].forEach((digit, index) => {
        if (otpRefs.current[index]?.current) {
          otpRefs.current[index].current.value = digit;
          const event = new Event('input', { bubbles: true });
          otpRefs.current[index].current.dispatchEvent(event);
        }
      });
      if (otpRefs.current[5]?.current) {
        otpRefs.current[5].current.focus();
      }
    }
  };

  const handleOtpInput = (e, index) => {
    const value = e.target.value;
    if (value && index < 5 && otpRefs.current[index + 1]?.current) {
      otpRefs.current[index + 1].current.focus();
    }
  };

  const handleOtpKeyDown = (e, index) => {
    if (
      e.key === 'Backspace' &&
      !e.target.value &&
      index > 0 &&
      otpRefs.current[index - 1]?.current
    ) {
      otpRefs.current[index - 1].current.focus();
    }
  };

  const handleResendCode = async () => {
    try {
      await forgotPassword(forgotPasswordEmail).unwrap();
      setResendDisabled(true);
      setResendTimer(30);
      const timer = setInterval(() => {
        setResendTimer((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            setResendDisabled(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } catch (err) {
      setErrMsg(err.data?.message || 'Failed to resend code');
    }
  };

  const renderForm = () => {
    switch (currentStep) {
      case FORGOT_STEPS.EMAIL:
        return (
          <ForgotPasswordForm
            register={register}
            handleSubmit={handleSubmit}
            errors={errors}
            onSubmit={onSubmit}
            isLoading={isForgotLoading}
            setCurrentStep={setCurrentStep}
            FORGOT_STEPS={FORGOT_STEPS}
            errMsg={errMsg}
            setErrMsg={setErrMsg}
          />
        );

      case FORGOT_STEPS.OTP:
        return (
          <OTPForm
            register={register}
            handleSubmit={handleSubmit}
            errors={errors}
            onSubmit={onSubmit}
            forgotPasswordEmail={forgotPasswordEmail}
            showPassword={showPassword}
            setShowPassword={setShowPassword}
            showConfirmPassword={showConfirmPassword}
            setShowConfirmPassword={setShowConfirmPassword}
            otpRefs={otpRefs}
            handleOtpPaste={handleOtpPaste}
            handleOtpInput={handleOtpInput}
            handleOtpKeyDown={handleOtpKeyDown}
            resendDisabled={resendDisabled}
            resendTimer={resendTimer}
            handleResendCode={handleResendCode}
            watch={watch}
            trigger={trigger}
            setCurrentStep={setCurrentStep}
            FORGOT_STEPS={FORGOT_STEPS}
            errMsg={errMsg}
            setErrMsg={setErrMsg}
            isLoading={isResetLoading}
            isResendLoading={isForgotLoading}
          />
        );

      default:
        return (
          <LoginForm
            register={register}
            handleSubmit={handleSubmit}
            errors={errors}
            showPassword={showPassword}
            setShowPassword={setShowPassword}
            rememberMe={rememberMe}
            setRememberMe={setRememberMe}
            setCurrentStep={setCurrentStep}
            FORGOT_STEPS={FORGOT_STEPS}
            onSubmit={onSubmit}
            isLoading={isLoading || isSsoLoading}
            errMsg={errMsg}
            setErrMsg={setErrMsg}
            handleSsoLogin={handleSsoLogin}
          />
        );
    }
  };

  if (isLoading) {
    return <Loading />;
  }

  return (
    <Box className="login-page">
      {/* Left side - Promo Area */}
      <PromoSection />

      {/* Right side - Form Area */}
      <Box className="login-page__form-area">
        <Box className="login-page__form-area-container">{renderForm()}</Box>
      </Box>

      <Onboarding open={showOnboarding} userData={userData} />
    </Box>
  );
};

export default LoginPage;
