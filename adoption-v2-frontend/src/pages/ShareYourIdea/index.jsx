import { Box, Container, Grid, Typography, CircularProgress } from '@mui/material';
import { useSelector } from 'react-redux';
import WelcomeHeader from '../../components/WelcomeHeader/WelcomeHeader.jsx';
import WhiteContainer from '../../components/WhiteContainer/WhiteContainer.jsx';
import Button from '../../components/Button/Button.jsx';
import FormModal from '../../components/FormModal/FormModal.jsx';
import FormRender from '../../components/FormRender/FormRender.jsx';
import './ShareYourIdea.scss';
import { useTranslation } from 'react-i18next';
import { useState, useEffect, useReducer } from 'react';
import { useFormByIdQuery } from '../../redux/services/form-service';
import { useSubmitFormResponseMutation } from '../../redux/services/form-response';
import PropTypes from 'prop-types';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import useIdeationList from '@/domains/form/hook/useIdeationList';
import SubmittedIdeaCard from '@/domains/form/components/SubmittedIdeaCard';
import InfoIcon from '@mui/icons-material/Info';
import { IDEA_FORM_ID } from '../../constants/form-constants';

// Fetch ideation list for all users for form responses here
// This way the hook conditional call error will be resolved
const useIdeationData = (userId, formType) => {
  const { ideationList, isIdeationLoading, isIdeationError } = useIdeationList(userId, formType);
  return { ideationList, isIdeationLoading, isIdeationError };
};

// Helper function to collect all fields of a form
const getAllFields = (formData) => {
  if (!formData) return [];

  let allFields = [];

  // Add fields from main fields array
  if (formData.fields && Array.isArray(formData.fields)) {
    allFields = [...formData.fields];
  }

  // Add fields from topics array
  if (formData.topics && Array.isArray(formData.topics)) {
    formData.topics.forEach((topic) => {
      if (topic.fields && Array.isArray(topic.fields)) {
        allFields = [...allFields, ...topic.fields];
      }
    });
  }

  return allFields;
};

const ShareYourIdeaPage = () => {
  const user = useSelector((state) => state.auth.user);
  const { t, i18n } = useTranslation();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({});
  const [descriptionFields, setDescriptionFields] = useState([]);
  const [classificationFields, setClassificationFields] = useState([]);
  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [skipValidationOnce, setSkipValidationOnce] = useState(true);
  const [hasInteracted, setHasInteracted] = useState(false);

  // Get current language selection
  const currentLang = i18n.language || 'en';

  // Instead of calling useIdeationList hook conditionally, we call it in a separate function
  const { ideationList, isIdeationLoading, isIdeationError } = useIdeationData(
    user ? user._id : '',
    'ideation'
  );

  // Simple forceUpdate function for React re-rendering
  const [, forceUpdate] = useReducer((x) => x + 1, 0);

  // Fetch form data - based on user's language selection
  const {
    data: apiFormData,
    isLoading,
    error,
  } = useFormByIdQuery(
    {
      formId: IDEA_FORM_ID,
      lang: currentLang,
    },
    {
      refetchOnMountOrArgChange: true, // Refetch on language change
    }
  );

  // Use RTK mutation hook to submit form responses
  const [submitFormResponse, { isLoading: isSubmittingForm }] = useSubmitFormResponseMutation();

  // Track state updates while form response is being submitted
  useEffect(() => {
    setIsSubmitting(isSubmittingForm);
  }, [isSubmittingForm]);

  useEffect(() => {}, [formData]);

  useEffect(() => {}, [formErrors]);

  useEffect(() => {
    if (isModalOpen) {
      setFormData({});
      setFormErrors({});
      setActiveStep(0);

      setTimeout(() => {
        const allFields = [...descriptionFields, ...classificationFields];

        const initialValues = {};

        allFields
          .filter((field) => field.type === 'select' && field.required)
          .forEach((field) => {
            initialValues[field.name] = null;
          });

        setFormData(initialValues);

        setFormErrors({});

        forceUpdate();
      }, 100);
    }
  }, [isModalOpen, descriptionFields, classificationFields]);

  useEffect(() => {
    if (apiFormData) {
      // Collect all form fields - from both fields and topics arrays
      let allFields = [];

      // Add fields from main fields array
      if (apiFormData?.data?.fields && apiFormData.data.fields.length > 0) {
        allFields = [...apiFormData.data.fields];
      } else if (apiFormData?.fields && apiFormData.fields.length > 0) {
        allFields = [...apiFormData.fields];
      } else if (apiFormData?.data?.forms && apiFormData.data.forms[0]?.fields) {
        allFields = [...apiFormData.data.forms[0].fields];
      }

      // Add fields from topics array
      if (apiFormData?.data?.topics && apiFormData.data.topics.length > 0) {
        apiFormData.data.topics.forEach((topic) => {
          if (topic.fields && topic.fields.length > 0) {
            allFields = [...allFields, ...topic.fields];
          }
        });
      } else if (apiFormData?.topics && apiFormData.topics.length > 0) {
        apiFormData.topics.forEach((topic) => {
          if (topic.fields && topic.fields.length > 0) {
            allFields = [...allFields, ...topic.fields];
          }
        });
      }

      // Filter title and description fields
      const filteredDescriptionFields = allFields.filter((field) => {
        const name = field.name?.toLowerCase().trim();
        return (
          name === 'title' ||
          name === 'description' ||
          name.includes('describe') ||
          name.includes('title')
        );
      });

      const descFields =
        filteredDescriptionFields.length > 0 ? filteredDescriptionFields : allFields.slice(0, 2);

      setDescriptionFields(descFields);

      // Filter other fields
      const filteredClassificationFields = allFields.filter((field) => {
        const name = field.name?.toLowerCase().trim();
        return (
          name !== 'title' &&
          name !== 'description' &&
          !name.includes('describe') &&
          !name.includes('title')
        );
      });

      const classFields =
        filteredClassificationFields.length > 0 ? filteredClassificationFields : allFields.slice(2);

      setClassificationFields(classFields);
    }
  }, [apiFormData]);

  const handleOpenModal = () => {
    setIsModalOpen(true);
    setSkipValidationOnce(true);
    setHasInteracted(false);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setActiveStep(0);
    setFormData({});
  };

  // Helper function to clean form fields
  const cleanHiddenFieldErrors = (currentFields, formState, allFieldsParam = []) => {
    // Check all fields and clear errors for hidden ones
    const updatedErrors = { ...formErrors };
    let hasChanges = false;

    currentFields.forEach((field) => {
      const isVisible = checkCondition(field, formState, allFieldsParam);
      if (!isVisible && updatedErrors[field.name]) {
        delete updatedErrors[field.name];
        hasChanges = true;
      }
    });

    // Update state if error list is updated
    if (hasChanges) {
      setFormErrors(updatedErrors);
      return updatedErrors;
    }

    return formErrors;
  };

  const handleSubmit = async () => {
    const allFields = [...descriptionFields, ...classificationFields];

    // First clear errors for hidden fields
    const currentErrors = cleanHiddenFieldErrors(allFields, formData, allFields);

    let missingRequiredField = false;
    const missingFields = [];

    allFields.forEach((field) => {
      // First check if field is visible
      const isVisible = checkCondition(field, formData, allFields);

      // If field is not visible, don't check and clear related error
      if (!isVisible) {
        // If there's an error for this field, let's clear it
        if (currentErrors[field.name]) {
          delete currentErrors[field.name];
        }
        return; // use return instead of continue because we're inside forEach
      }

      // Only do required check for visible fields
      if (field.required && checkIfEmpty(formData[field.name])) {
        missingRequiredField = true;
        missingFields.push(field.label || field.name);
        currentErrors[field.name] = 'This field is required';
      }
    });

    // Set updated errors
    setFormErrors(currentErrors);

    if (missingRequiredField) {
      toast.error(t('ideation.form.errors.requiredFields'), {
        toastId: 'idea-form-error',
      });
      return false;
    }

    try {
      const responses = allFields
        .filter((field) => {
          // Check visible fields
          const isVisible = checkCondition(field, formData, allFields);
          if (!isVisible) return false;

          // Filter filled visible fields
          return !checkIfEmpty(formData[field.name]);
        })
        .map((field) => ({
          fieldId: field._id,
          name: field.name,
          type: field.type,
          value: formData[field.name],
        }));

      const requestData = {
        formId: IDEA_FORM_ID,
        formType: 'ideation',
        title: apiFormData?.data?.title || apiFormData?.title || t('ideation.form.defaultTitle'),
        description:
          apiFormData?.data?.description ||
          apiFormData?.description ||
          t('ideation.form.defaultDescription'),
        responses,
        source: 'share-your-idea-page',
        sourceDetails: {},
      };

      const response = await submitFormResponse(requestData).unwrap();

      if (response && response.status === 'success') {
        toast.success(t('ideation.form.success.submitted'), {
          toastId: 'idea-form-success',
        });

        handleCloseModal();
        return true;
      } else {
        const errorMessage = response?.message || t('ideation.form.errors.submission');
        console.error('Form submission failed:', errorMessage);
        toast.error(`${t('common.error')}: ${errorMessage}. ${t('common.tryAgain')}`, {
          toastId: 'idea-form-submission-error',
        });
        return false;
      }
    } catch (error) {
      const errorMessage =
        error.data?.message || error.message || t('ideation.form.errors.generic');

      console.error('Form submission error:', error);
      toast.error(`${t('common.error')}: ${errorMessage}. ${t('common.tryAgain')}`, {
        toastId: 'idea-form-catch-error',
      });
      return false;
    }
  };

  const handleFormChange = (newData) => {
    if (!newData || Object.keys(newData).length === 0) {
      return;
    }

    // Set flag to true when user interacts with form
    if (newData.__userInteracted) {
      setHasInteracted(true);
    }

    if (newData.__errors) {
      const updatedErrors = { ...formErrors };

      Object.entries(newData.__errors).forEach(([key, value]) => {
        if (value === null) {
          // Error is being cleared
          delete updatedErrors[key];
        } else {
          // New error is being added
          updatedErrors[key] = value;
        }
      });

      // Update validation errors
      setFormErrors(updatedErrors);
      delete newData.__errors;
    }

    // Data from form page
    if (newData.__formState) {
      // In this case, we can directly update all form data
      setFormData(newData.__formState);
      delete newData.__formState;
    } else {
      // Clear __userInteracted and __formState special fields
      delete newData.__userInteracted;

      // Update form data
      const updatedFields = Object.keys(newData).filter((k) => k !== '__errors');
      if (updatedFields.length > 0) {
        const updatedFormData = { ...formData };

        updatedFields.forEach((fieldName) => {
          updatedFormData[fieldName] = newData[fieldName];

          // If field is filled and there's an error, clear the error
          if (!checkIfEmpty(newData[fieldName]) && formErrors[fieldName]) {
            const updatedErrors = { ...formErrors };
            delete updatedErrors[fieldName];
            setFormErrors(updatedErrors);
          }
        });

        setFormData(updatedFormData);

        // Update form and log data for debugging
        setTimeout(() => {
          forceUpdate();
        }, 10);
      }
    }
  };

  // Function to check field condition status
  const checkCondition = (field, formValues, allFieldsParam = []) => {
    // Always show basic fields - business_benefit and business_area
    if (field.name === 'business_benefit' || field.name === 'business_area') {
      return true;
    }

    // Visible if no condition
    if (!field.condition && !field.conditional_logic) return true;

    // Log condition information for debugging

    // "condition" property from API
    if (field.condition) {
      const { field: conditionField, operator, value } = field.condition;

      // Get condition field value
      const fieldValue = formValues[conditionField];

      // Operator check
      let result = false;
      switch (operator) {
        case 'equals':
          result = fieldValue === value;
          break;
        case 'not_equals':
          result = fieldValue !== value;
          break;
        case 'contains':
          result = typeof fieldValue === 'string' && fieldValue.includes(value);
          break;
        case 'not_contains':
          result = typeof fieldValue === 'string' && !fieldValue.includes(value);
          break;
        case 'starts_with':
          result = typeof fieldValue === 'string' && fieldValue.startsWith(value);
          break;
        case 'ends_with':
          result = typeof fieldValue === 'string' && fieldValue.endsWith(value);
          break;
        case 'greater_than':
          result = Number(fieldValue) > Number(value);
          break;
        case 'less_than':
          result = Number(fieldValue) < Number(value);
          break;
        default:
          result = true;
          break;
      }

      return result;
    }

    // "conditional_logic" property compatible with FormRender component
    if (field.conditional_logic && field.conditional_logic.enabled) {
      // Evaluate condition groups (each group is evaluated with OR relationship)
      return field.conditional_logic.rules.some((ruleGroup) => {
        // Evaluate rules in each group (each rule is evaluated with AND relationship)
        return ruleGroup.every((rule) => {
          // Skip rule if empty field name exists
          if (!rule.field) {
            return true;
          }

          const fieldValue = formValues[rule.field];

          switch (rule.operator) {
            case 'equals':
            case '=':
            case '==':
              return fieldValue === rule.value;
            case 'not_equals':
            case '!=':
              // For empty value comparison, check if field has any value
              if (rule.value === '') {
                return fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
              }
              return fieldValue !== rule.value;
            case 'has_any_value':
              return fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
            case 'is_empty':
              return fieldValue === undefined || fieldValue === null || fieldValue === '';
            case '>':
            case 'greater_than':
              return parseFloat(fieldValue) > parseFloat(rule.value);
            case '<':
            case 'less_than':
              return parseFloat(fieldValue) < parseFloat(rule.value);
            case 'contains':
              // For select fields, check both value and label
              if (typeof fieldValue === 'string') {
                // First check direct string contains
                if (fieldValue.includes(rule.value)) {
                  return true;
                }

                // For select fields, also check if the selected option's label contains the rule value
                const sourceField = allFieldsParam.find((f) => f.name === rule.field);
                if (sourceField && sourceField.type === 'select' && sourceField.options) {
                  const selectedOption = sourceField.options.find(
                    (opt) => opt.value === fieldValue
                  );
                  if (selectedOption && selectedOption.label.includes(rule.value)) {
                    return true;
                  }
                }
              }
              return false;
            case 'starts_with':
              return typeof fieldValue === 'string' && fieldValue.startsWith(rule.value);
            case 'ends_with':
              return typeof fieldValue === 'string' && fieldValue.endsWith(rule.value);
            default:
              return true;
          }
        });
      });
    }

    // Consider field visible by default
    return true;
  };

  const checkIfEmpty = (value) => {
    if (value === undefined || value === null) {
      return true;
    }

    if (typeof value === 'string') {
      const isEmpty = value.trim() === '';
      return isEmpty;
    }

    if (Array.isArray(value)) {
      return value.length === 0;
    }

    if (typeof value === 'object' && !Array.isArray(value)) {
      return Object.keys(value).length === 0;
    }

    return false;
  };

  if (!user) {
    return (
      <Container>
        <Box sx={{ py: 4 }}>
          <Typography variant="h4">Please log in.</Typography>
        </Box>
      </Container>
    );
  }

  // Prepare current form data structure compatible with FormRender component
  const prepareFormData = (formId, title, description, fields) => {
    return {
      _id: formId,
      title: title,
      description: description,
      type: 'form',
      fields: fields,
    };
  };

  const formSteps = [
    {
      title: t('ideation.form.steps.describe.title'),
      label: t('ideation.form.steps.describe.label'),
      onChange: (data) => {
        handleFormChange(data);
      },
      content: (
        <FormRender
          hideTitle={false}
          hideDescription={false}
          formTitle={t('ideation.form.steps.describe.title')}
          formData={prepareFormData(
            apiFormData?.data?._id || apiFormData?._id || 'idea-form',
            apiFormData?.data?.title || apiFormData?.title || t('ideation.form.title'),
            apiFormData?.data?.description || apiFormData?.description || '',
            descriptionFields.length > 0 ? descriptionFields : []
          )}
          onChange={(data) => {
            handleFormChange(data);
          }}
          values={formData}
          errors={formErrors}
          disabled={isSubmitting}
        />
      ),
    },
    {
      title: t('ideation.form.steps.classify.title'),
      label: t('ideation.form.steps.classify.label'),
      onChange: (data) => {
        handleFormChange(data);
      },
      content: (
        <FormRender
          hideTitle={false}
          hideDescription={true}
          formTitle={t('ideation.form.steps.classify.title')}
          formData={prepareFormData(
            apiFormData?.data?._id || apiFormData?._id || 'idea-form',
            apiFormData?.data?.title || apiFormData?.title || t('ideation.form.title'),
            apiFormData?.data?.description || apiFormData?.description || '',
            classificationFields.length > 0 ? classificationFields : []
          )}
          data={{}}
          onChange={(data) => {
            handleFormChange(data);
          }}
          disabled={isSubmitting}
        />
      ),
    },
  ];

  return (
    <Container>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <WelcomeHeader
            isPageView={true}
            title={t('shareYourIdea.title')}
            description={t('shareYourIdea.description')}
            showProgress={false}
          />
          <Box mt={3}>
            <Button variant="contained" color="primary" size="medium" onClick={handleOpenModal}>
              {t('shareYourIdea.buttons.NewIdea')}
            </Button>
          </Box>
        </Grid>
        <Grid item xs={12}>
          <WhiteContainer
            title={t('ideation.submissions.title')}
            subtitle={t('ideation.submissions.subtitle')}
          >
            <Grid container spacing={3}>
              {isIdeationLoading ? (
                <Grid item xs={12}>
                  <Box display="flex" justifyContent="center" p={3}>
                    <CircularProgress />
                  </Box>
                </Grid>
              ) : isIdeationError ? (
                <Grid item xs={12}>
                  <Typography color="error">
                    {t('common.error')}: {isIdeationError.message}
                  </Typography>
                </Grid>
              ) : ideationList && ideationList.length > 0 ? (
                ideationList.map((idea, index) => (
                  <SubmittedIdeaCard key={index} idea={idea} formData={apiFormData?.data} />
                ))
              ) : (
                <Grid item xs={12}>
                  <Box className="state-message--info">
                    <InfoIcon className="message-icon" />
                    {t('ideation.submissions.noIdeas')}
                  </Box>
                </Grid>
              )}
            </Grid>
          </WhiteContainer>
        </Grid>
      </Grid>
      <FormModal
        open={isModalOpen}
        onClose={handleCloseModal}
        title={t('shareYourIdea.modal.title')}
        onSubmit={handleSubmit}
        maxWidth="md"
        activeStep={activeStep}
        steps={formSteps}
        onNext={() => {
          // Skip validation if user hasn't interacted with form yet
          // This only works in first step -> second step transition
          if (skipValidationOnce && !hasInteracted) {
            setSkipValidationOnce(false);
            return true;
          }

          // Get form fields shown in current step
          const currentFields = activeStep === 0 ? descriptionFields : classificationFields;
          const allFields = [...descriptionFields, ...classificationFields];

          // Clear errors for hidden fields
          const cleanedErrors = cleanHiddenFieldErrors(currentFields, formData, allFields);

          // Check if all required fields are filled
          let allValid = true;
          const newErrors = {};

          // Check if each required field is empty
          for (const field of currentFields) {
            // First check if field is visible
            const isVisible = checkCondition(field, formData, allFields);

            // Don't check if field is not visible
            if (!isVisible) {
              continue;
            }

            // Only do required check for visible fields
            if (field.required) {
              const fieldValue = formData[field.name];
              const isEmpty = checkIfEmpty(fieldValue);

              if (isEmpty) {
                newErrors[field.name] = t('common.validation.required');
                allValid = false;
              }
            }
          }

          // If any required field is empty, set errors and return false
          if (!allValid) {
            setFormErrors({
              ...cleanedErrors,
              ...newErrors,
            });
            // If returns false, FormModal will not proceed to next step
            return false;
          }

          return true;
        }}
        showReview={true}
        formErrors={formErrors}
        loading={isSubmitting}
        skipInitialValidation={true}
        values={formData}
      >
        {isLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Typography color="error">Form not found. Please try again later.</Typography>
        ) : isSubmitting ? (
          <Box sx={{ position: 'relative' }}>
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'rgba(255, 255, 255, 0.7)',
                zIndex: 2,
              }}
            >
              <Box sx={{ textAlign: 'center' }}>
                <CircularProgress size={60} />
                <Typography sx={{ mt: 2 }}>Submitting your idea...</Typography>
              </Box>
            </Box>
            {formSteps && formSteps.length > 0 ? (
              formSteps[activeStep < formSteps.length ? activeStep : 0].content
            ) : (
              <Typography>No form steps defined</Typography>
            )}
          </Box>
        ) : formSteps && formSteps.length > 0 ? (
          formSteps[activeStep < formSteps.length ? activeStep : 0].content
        ) : (
          <Typography>No form steps defined</Typography>
        )}
      </FormModal>
    </Container>
  );
};

ShareYourIdeaPage.propTypes = {
  data: PropTypes.object,
  onChange: PropTypes.func,
};

export default ShareYourIdeaPage;
