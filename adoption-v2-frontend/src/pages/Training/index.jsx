import { Suspense } from 'react';
import { Box, Container, Grid, Typography, CircularProgress } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import Carousel from '../../components/Carousel/Carousel.jsx';
import CourseCard, { CardContent as CourseCardContent } from '@/components/CourseCard/CourseCard';
import WhiteContainer from '../../components/WhiteContainer/WhiteContainer.jsx';
import './Training.scss';
import PropTypes from 'prop-types';
import CardWithIcon from '../../components/CardWithIcon/CardWithIcon';
import { personalizeApi } from '../../middleware/personalize-api';
import { platformSettingsApi } from '../../redux/services/platform-settings-api';
import { segmentEvaluator } from '../../middleware/segment-evaluator';
import { checkAndLockCards } from '../../utils/cardUtils';
const { useGetUserSegmentationQuery } = platformSettingsApi;
import { useNavigate } from 'react-router-dom';
 
import { techCompanies } from '../../mockData/techCompanies';
import { recommendedTrainingTopics } from '../../mockData/recommendedTrainingTopics';
import { technicalSpecialistTrainings } from '../../mockData/technicalSpecialistTrainings';
import JourneySection from '../../domains/journey/components/JourneySection';
import { setCurrentCardId } from '../../redux/features/courses/courseSlice.js';

const TrainingContent = () => {
  const { t, i18n } = useTranslation();
  const user = useSelector((state) => state.auth.user);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { data: userSegmentation } = useGetUserSegmentationQuery();
 
  const userSegment = segmentEvaluator.findUserSegment(userSegmentation?.data?.segments, {
    function_label: user?.onboarding?.function_label,
    technical_background_label: user?.onboarding?.technical_background_label,
    ai_knowledge_label: user?.onboarding?.ai_knowledge_label,
    job_role_label: user?.onboarding?.job_role_label,
    management_role_label: user?.onboarding?.management_role_label,
    industry_label: user?.onboarding?.industry_label,
  });
 
  let filteredCompanies = [];
  let filteredRecommendedTrainingTopics = [];
 
  const filteredTechnicalSpecialistTrainings = technicalSpecialistTrainings.map((training) => ({
    type: 'technicalSpecialistTrainingCard',
    props: { ...training },
  }));
 
  if (user?.onboarding) {
    const tempFilteredRecommendedTrainingTopics = personalizeApi.filterPageContent(
      recommendedTrainingTopics.map((training) => ({
        type: 'recommendedTrainingTopicCard',
        props: { ...training },
      })),
      { ...user?.onboarding, segment: userSegment }
    );
    filteredRecommendedTrainingTopics = tempFilteredRecommendedTrainingTopics;
 
    const tempFilteredCompanies = personalizeApi.filterPageContent(
      techCompanies.map((company) => ({
        type: 'CompanyCard',
        props: { ...company },
      })),
      {
        ...user?.onboarding,
        segment: userSegment,
      }
    ); 
    filteredCompanies = checkAndLockCards(tempFilteredCompanies, user, t);
  }

  if (!user) {
    return (
      <Container>
        <Box sx={{ py: 4 }}>
          <Typography variant="h4">Please log in.</Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <JourneySection
            user={user}
            containerTitle={t('training.title')}
            showUserProgress={true}
          />
        </Grid>

        <Grid item xs={12} mb={1}>
          <WhiteContainer
            title={t('training.recommended')}
            subtitle={t('training.recommendedSubtitle')}
            showNavigation={true}
            variant="transparent"
          >
            <Carousel
              swiperProps={{
                slidesPerView: 'auto',
                spaceBetween: 24,
              }}
              breakpoints={{
                320: { slidesPerView: 1, spaceBetween: 24 },
                768: { slidesPerView: 2, spaceBetween: 24 },
                1024: { slidesPerView: 3, spaceBetween: 24 },
                1440: { slidesPerView: 3, spaceBetween: 24 },
              }}
            >
              {filteredRecommendedTrainingTopics.map((item) => (
                <CourseCard
                  key={item.props.id}
                  buttonText={i18n.language === 'en' ? 'View' : 'Mehr anzeigen'}
                  buttonType="URL"
                  percent={item.props.percent}
                  buttonVariant="text"
                  imageSrc={item.props.image}
                  onClick={() => { 
                    sessionStorage.removeItem('journeyCardId');
                    sessionStorage.removeItem('journeyLevel'); 
                    dispatch(setCurrentCardId(null));
                    navigate(`/course/${item.props.id}`);
                  }}
                >
                  <CourseCardContent
                    title={
                      i18n.language === 'en'
                        ? item.props.translation.en.title
                        : item.props.translation.de.title
                    }
                    description={
                      i18n.language === 'en'
                        ? item.props.translation.en.description
                        : item.props.translation.de.description
                    }
                    buttonURL={`/course/${item.props.id}`}
                    newTab={item.props.newTab}
                    buttonText={i18n.language === 'en' ? 'View' : 'Mehr anzeigen'}
                    buttonType={item.props.buttonType}
                    imageSrc={item.props.image}
                  />
                </CourseCard>
              ))}
            </Carousel>
          </WhiteContainer>
        </Grid>

        <Grid item xs={12} mb={0}>
          <WhiteContainer
            title={t('training.toolSpecific.title')}
            subtitle={t('training.toolSpecific.subtitle')}
            showNavigation={true}
            variant="transparent"
          >
            <Carousel
              swiperProps={{
                slidesPerView: 'auto',
                spaceBetween: 24,
              }}
              breakpoints={{
                320: { slidesPerView: 2, spaceBetween: 24 },
                768: { slidesPerView: 4, spaceBetween: 24 },
                1024: { slidesPerView: 5, spaceBetween: 24 },
                1440: { slidesPerView: 5, spaceBetween: 24 },
              }}
            >
              {filteredCompanies.map((company) => (
                <CardWithIcon
                  key={company.props.id}
                  image={company.props.image}
                  title={company.props.title}
                  description=""
                  locked={company.props.locked}
                  tooltipText={
                    company.props.locked
                      ? company.props.tooltipText || t('common.lockedTooltip')
                      : undefined
                  }
                  buttonType={company.props.buttonType}
                  variant={company.props.variant}
                  buttonURL={company.props.buttonURL}
                  class={company.props.class}
                  onClick={() => {
                    if (company.props.locked) return;
                    if (company.props.newTab) {
                      window.open(company.props.buttonURL, '_blank');
                    } else {
                      navigate(company.props.buttonURL);
                    }
                  }}
                />
              ))}
            </Carousel>
          </WhiteContainer>
        </Grid>

        {filteredTechnicalSpecialistTrainings.length > 0 && (
          <Grid item xs={12} mb={4}>
            <WhiteContainer
              title={t('training.technicalSpecialists.title')}
              subtitle={t('training.technicalSpecialists.subtitle')}
              showNavigation={true}
              variant="transparent"
            >
              <Carousel
                swiperProps={{
                  slidesPerView: 'auto',
                  spaceBetween: 24,
                }}
                breakpoints={{
                  320: { slidesPerView: 1, spaceBetween: 24 },
                  768: { slidesPerView: 2, spaceBetween: 24 },
                  1024: { slidesPerView: 3, spaceBetween: 24 },
                  1440: { slidesPerView: 3, spaceBetween: 24 },
                }}
              >
                {filteredTechnicalSpecialistTrainings.map((item) => (
                  <CourseCard
                    key={item.props.id}
                    locked={item.props.lock}
                    buttonText={i18n.language === 'en' ? 'View' : 'Mehr anzeigen'}
                    buttonType="URL"
                    percent={item.props.percent}
                    tooltipText={t('common.lockedTooltip')}
                    buttonVariant="text"
                    imageSrc={item.props.image}
                    onClick={() => { 
                      sessionStorage.removeItem('journeyCardId');
                      sessionStorage.removeItem('journeyLevel'); 
                      dispatch(setCurrentCardId(null));
                      navigate(`/trainings/${item.props.slug}`);
                    }}
                  >
                    <CourseCardContent
                      title={
                        i18n.language === 'en'
                          ? item.props.translation.en.title
                          : item.props.translation.de.title
                      }
                      description={
                        i18n.language === 'en'
                          ? item.props.translation.en.description
                          : item.props.translation.de.description
                      }
                      buttonURL={`/trainings/${item.props.slug}`}
                      newTab={item.props.newTab}
                      buttonText={i18n.language === 'en' ? 'View' : 'Mehr anzeigen'}
                      buttonType={item.props.buttonType}
                      imageSrc={item.props.image}
                    />
                  </CourseCard>
                ))}
              </Carousel>
            </WhiteContainer>
          </Grid>
        )}
      </Grid>
    </Container>
  );
};

CourseCardContent.propTypes = {
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  buttonURL: PropTypes.string,
  newTab: PropTypes.bool,
  buttonText: PropTypes.string,
  buttonType: PropTypes.string,
  imageSrc: PropTypes.string,
};

const Training = () => {
  return (
    <Suspense
      fallback={
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: 'calc(100vh - 64px)',  
          }}
        >
          <CircularProgress />
        </Box>
      }
    >
      <TrainingContent />
    </Suspense>
  );
};

export default Training;
