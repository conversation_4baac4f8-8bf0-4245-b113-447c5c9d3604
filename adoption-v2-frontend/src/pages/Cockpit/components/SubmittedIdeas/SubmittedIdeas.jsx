import React from 'react';
import { Box, Typography, Grid } from '@mui/material';
import { useTranslation } from 'react-i18next';
import styles from '../../Cockpit.module.scss';
import useIdeationList from '@/domains/form/hook/useIdeationList';
import { useSelector } from 'react-redux';
import SubmittedIdeaCard from '@/domains/form/components/SubmittedIdeaCard'; 
import { IDEA_FORM_ID } from '../../../../constants/form-constants'; 
import { useFormByIdQuery } from '@/redux/services/form-service';
const SubmittedIdeas = () => {
  const { t, i18n } = useTranslation(); 
  const currentLang = i18n.language || 'en';
  const user = useSelector((state) => state.auth.user); 
  const {
    data: apiFormData,
    isFormLoading,
    isFormError
  } = useFormByIdQuery(
    {
      formId: IDEA_FORM_ID,
      lang: currentLang,
    },
    {
      refetchOnMountOrArgChange: true, // Refetch on language change
    }
  );
  if (isFormLoading) {
    return <div>Loading...</div>;
  }
  const { ideationList, isLoading, error } = useIdeationList(user?._id, 'ideation');

  if (isLoading) {
    return <div>Loading...</div>;  
  }

  if (error) {
    return <div>Error: {error.message}</div>;  
  } 
  return (
    <Box sx={{ flexGrow: 1, width: '100%' }} className={styles.appCardsContainer}>
      <Typography variant="h6" sx={{ mb: 1 }}>
        {t('cockpit.submittedIdeas.title')}
      </Typography>
      <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 3 }}>
        {t('cockpit.submittedIdeas.subtitle')}
      </Typography>
      <Grid container spacing={2} sx={{ mt: 2 }}>
        
        {ideationList.map((idea, index) => (
          <SubmittedIdeaCard key={index} idea={idea} formData={apiFormData?.data} />
        ))}
      </Grid>
    </Box>
  );
};
 

export default SubmittedIdeas;
