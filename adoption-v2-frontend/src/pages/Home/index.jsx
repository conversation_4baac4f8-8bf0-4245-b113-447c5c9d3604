import { useState, Suspense } from 'react';
import { Box, Container, Grid, Typography, CircularProgress } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import Carousel from '../../components/Carousel/Carousel.jsx';
import CourseCard, {
  CardContent as CourseCardContent,
} from '../../components/CourseCard/CourseCard';
import WelcomeHeader from '../../components/WelcomeHeader/WelcomeHeader.jsx';
import WhiteContainer from '../../components/WhiteContainer/WhiteContainer.jsx';
import './home.scss';
import PropTypes from 'prop-types';
import AIModal from '../../components/AIModal/AIModal';
import AiChat from '../../components/AiChat/AiChat';

import { useNavigate } from 'react-router-dom';

import CardWithIcon from '../../components/CardWithIcon/CardWithIcon';

import { useCases as getUserCases } from '../../mockData/useCases.js';
import { aiPlaygrounds } from '../../mockData/aiPlaygrounds.js';
import { techCompanies } from '../../mockData/techCompanies.js';
import { recommendedTrainingTopics } from '../../mockData/recommendedTrainingTopics.js';
import { personalizeApi } from '../../middleware/personalize-api';
import { platformSettingsApi } from '../../redux/services/platform-settings-api';
import { segmentEvaluator } from '../../middleware/segment-evaluator';
import { checkAndLockCards } from '../../utils/cardUtils';
import { useGetUserShortcutsQuery } from '../../redux/services/shortcuts-api';

const { useGetUserSegmentationQuery, useGetGeneralSettingsQuery } = platformSettingsApi;

import { useTranslation } from 'react-i18next';
import JourneySection from '../../domains/journey/components/JourneySection';
import { setCurrentCardId } from '../../redux/features/courses/courseSlice.js';

const HomePageContent = () => {
  const { t, i18n } = useTranslation();
  const user = useSelector((state) => state.auth.user);
  const navigate = useNavigate();
  const dispatch = useDispatch();

  // Platform video modal state
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);
  const [videoUrl, setVideoUrl] = useState('');

  // Fetch general settings to check enableAiChat
  const { data: generalSettingsData } = useGetGeneralSettingsQuery();

  // Check if AI chat feature is enabled in general settings
  const isAiChatEnabled = generalSettingsData?.data?.enableAiChat === true;

  // Fetch user shortcuts (favorites)
  const { data: userShortcuts } = useGetUserShortcutsQuery(
    {
      userId: user?._id,
      shortcutType: '',
    },
    {
      skip: !user?._id,
      refetchOnMountOrArgChange: true, // Refetch query when component is remounted or arguments change
      refetchOnFocus: true, // Refetch query when page gains focus
      refetchOnReconnect: true, // Refetch query when connection is reestablished
    }
  );

  // Filter valid shortcuts
  const validShortcuts = userShortcuts
    ? userShortcuts.filter((shortcut) => {
        if (!shortcut.apiData) {
          return false;
        }

        if (shortcut.apiError) {
          return false;
        }

        if (!shortcut.apiData.title) {
          return false;
        }

        return true;
      })
    : [];

  // Check if user has any favorites
  const hasAnyFavorites = validShortcuts.length > 0;

  let filteredCompanies = [];
  let filteredUsecases = [];
  let filteredPlaygrounds = [];
  let filteredRecommendedTrainingTopics = [];
  // Runs on every render
  const { data: userSegmentation } = useGetUserSegmentationQuery();

  // Memoize segment calculation
  const userSegment = segmentEvaluator.findUserSegment(userSegmentation?.data?.segments, {
    function_label: user?.onboarding?.function_label,
    technical_background_label: user?.onboarding?.technical_background_label,
    ai_knowledge_label: user?.onboarding?.ai_knowledge_label,
    job_role_label: user?.onboarding?.job_role_label,
    management_role_label: user?.onboarding?.management_role_label,
    industry_label: user?.onboarding?.industry_label,
  });

  // Call useCases function and assign result to a variable
  const userCasesData = user ? getUserCases(user) : [];

  // Perform filtering operations
  filteredCompanies = user?.onboarding
    ? checkAndLockCards(
        personalizeApi.filterPageContent(
          techCompanies.map((company) => ({
            type: 'CompanyCard',
            props: { ...company },
          })),
          {
            ...user?.onboarding,
            segment: userSegment,
          }
        ),
        user,
        t
      )
    : [];

  // Filter useCases - exclude "Your personal favorites" card (id: 'uc1') if user has no favorites
  // and exclude "copilot-playground" card if AI chat is disabled
  filteredUsecases = user?.onboarding
    ? personalizeApi.filterPageContent(
        userCasesData
          .filter((usecase) => {
            // If it's the favorites card (uc1) and user has no favorites, exclude it
            if (usecase.id === 'uc1' && !hasAnyFavorites) {
              return false;
            }
            // If it's the copilot-playground card and AI chat is disabled, exclude it
            if (usecase.id === 'copilot-playground' && !isAiChatEnabled) {
              return false;
            }
            return true;
          })
          .map((usecase) => ({
            type: 'UseCaseCard',
            props: { ...usecase },
          })),
        { ...user?.onboarding, segment: userSegment }
      )
    : [];

  filteredPlaygrounds = user?.onboarding
    ? checkAndLockCards(
        personalizeApi.filterPageContent(
          aiPlaygrounds.map((playground) => ({
            type: 'PlaygroundCard',
            props: { ...playground },
          })),
          {
            ...user?.onboarding,
            segment: userSegment,
          }
        ),
        user,
        t
      )
    : [];

  // Filter recommended training topics
  filteredRecommendedTrainingTopics = user?.onboarding
    ? personalizeApi.filterPageContent(
        recommendedTrainingTopics.map((training) => ({
          type: 'recommendedTrainingTopicCard',
          props: { ...training },
        })),
        { ...user?.onboarding, segment: userSegment }
      )
    : [];

  if (!user) {
    return (
      <Container>
        <Box sx={{ py: 4 }}>
          <Typography variant="h4">Please log in.</Typography>
        </Box>
      </Container>
    );
  }

  const handleCardClickUseCase = (useCase) => {
    if (useCase.locked) return;

    if (useCase.buttonURL.includes('#')) {
      // Special handling for URLs containing hash
      const [path, hash] = useCase.buttonURL.split('#');

      // Navigate to page first
      navigate(path);

      // Perform scroll operation after page transition is complete
      setTimeout(() => {
        const element = document.getElementById(hash.replace('/', '')); // Remove / character from hash
        if (element) {
          element.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
          });
        }
      }, 500); // Increased timeout duration
    } else {
      // Direct navigation for normal URLs
      navigate(useCase.buttonURL);
    }
  };
  return (
    <Container>
      <Grid container spacing={4}>
        <Grid item xs={12}>
          <WelcomeHeader
            name={user?.name}
            functionLabel={user?.onboarding?.function_label}
            jobRoleLabel={user?.onboarding?.job_role_label}
            showProgress={false}
            buttons={[
              {
                label: t('home.watchIntroVideo', 'Platform video'),
                onClick: () => {
                  setVideoUrl(t('home.PlatformVideoUrl'));
                  setIsVideoModalOpen(true);
                },
                variant: 'outlined',
                color: 'primary',
              },
            ]}
          />
        </Grid>

        {/* Journey Section - Using newly created JourneySection component */}
        <Grid item xs={12}>
          <JourneySection
            user={user}
            showSkipJourney={true}
            hideSection={user?.trackingData?.seenModals?.includes('master_cards_completed')}
          />
        </Grid>

        {/* Recommended trainings topics section - Hide when user has completed the expert journey */}
        {user?.trackingData?.seenModals?.includes('master_cards_completed') && (
          <Grid item xs={12}>
            <WhiteContainer
              title={t('training.recommended', 'Recommended trainings topics')}
              subtitle={t(
                'training.recommendedSubtitle',
                'Explore these curated training topics to enhance your AI skills'
              )}
              showNavigation={true}
              variant="transparent"
            >
              <Carousel
                swiperProps={{
                  slidesPerView: 'auto',
                  spaceBetween: 24,
                }}
                breakpoints={{
                  320: { slidesPerView: 1, spaceBetween: 24 },
                  768: { slidesPerView: 2, spaceBetween: 24 },
                  1024: { slidesPerView: 3, spaceBetween: 24 },
                  1440: { slidesPerView: 3, spaceBetween: 24 },
                }}
              >
                {filteredRecommendedTrainingTopics.map((item) => (
                  <CourseCard
                    key={item.props.id}
                    buttonText={i18n.language === 'en' ? 'View' : 'Mehr anzeigen'}
                    buttonType="URL"
                    percent={item.props.percent}
                    buttonVariant="text"
                    imageSrc={item.props.image}
                    onClick={() => {
                      sessionStorage.removeItem('journeyCardId');
                      sessionStorage.removeItem('journeyLevel');
                      dispatch(setCurrentCardId(null));
                      navigate(`/course/${item.props.id}`);
                    }}
                  >
                    <CourseCardContent
                      title={
                        i18n.language === 'en'
                          ? item.props.translation.en.title
                          : item.props.translation.de.title
                      }
                      description={
                        i18n.language === 'en'
                          ? item.props.translation.en.description
                          : item.props.translation.de.description
                      }
                      buttonURL={`/course/${item.props.id}`}
                      newTab={item.props.newTab}
                      buttonText={i18n.language === 'en' ? 'View' : 'Mehr anzeigen'}
                      buttonType={item.props.buttonType}
                      imageSrc={item.props.image}
                    />
                  </CourseCard>
                ))}
              </Carousel>
            </WhiteContainer>
          </Grid>
        )}

        <Grid item xs={12}>
          <WhiteContainer
            title={t('home.dailyAI.title')}
            subtitle={t('home.dailyAI.subtitle')}
            variant="transparent"
            showNavigation={true}
          >
            <Carousel
              swiperProps={{
                slidesPerView: 'auto',
                spaceBetween: 24,
              }}
              breakpoints={{
                320: { slidesPerView: 1, spaceBetween: 24 },
                768: { slidesPerView: 2, spaceBetween: 24 },
                1024: { slidesPerView: 3, spaceBetween: 24 },
                1440: { slidesPerView: 4, spaceBetween: 24 },
              }}
            >
              {filteredUsecases.map((useCase) => (
                <CardWithIcon
                  key={useCase.props.id}
                  icon={useCase.props.icon ? <useCase.props.icon /> : undefined}
                  image={useCase.props.image}
                  title={
                    useCase.props.translations?.title?.[i18n.language] ||
                    useCase.props.translations?.title?.['en'] ||
                    useCase.props.title
                  }
                  description={
                    useCase.props.translations?.description?.[i18n.language] ||
                    useCase.props.translations?.description?.['en'] ||
                    useCase.props.description
                  }
                  variant={useCase.props.variant}
                  locked={useCase.props.locked}
                  tooltipText={
                    useCase.props.locked
                      ? useCase.props.translations?.tooltipText?.[i18n.language] ||
                        useCase.props.translations?.tooltipText?.['en'] ||
                        t('common.lockedTooltip')
                      : undefined
                  }
                  buttonType={useCase.props.buttonType}
                  buttonURL={useCase.props.buttonURL}
                  newTab={useCase.props.newTab}
                  onClick={() => handleCardClickUseCase(useCase.props)}
                />
              ))}
            </Carousel>
          </WhiteContainer>
        </Grid>

        {/* AI Playgrounds section */}
        <Grid item xs={12}>
          <WhiteContainer
            title={t('home.aiPlaygrounds.title')}
            subtitle={t('home.aiPlaygrounds.subtitle')}
            showNavigation={true}
            variant="transparent"
          >
            <Carousel
              swiperProps={{
                slidesPerView: 'auto',
                spaceBetween: 24,
              }}
              breakpoints={{
                320: { slidesPerView: 1, spaceBetween: 24 },
                768: { slidesPerView: 2, spaceBetween: 24 },
                1024: { slidesPerView: 3, spaceBetween: 24 },
                1440: { slidesPerView: 4, spaceBetween: 24 },
              }}
            >
              {filteredPlaygrounds.map((playground) => (
                <CardWithIcon
                  key={playground.props.id}
                  icon={
                    <img
                      src={playground.props.icon}
                      alt={playground.props.iconAlt}
                      style={playground.props.iconStyle}
                    />
                  }
                  title={
                    playground.props.translations?.title?.[i18n.language] ||
                    playground.props.translations?.title?.['en'] ||
                    playground.props.title
                  }
                  description={
                    playground.props.translations?.description?.[i18n.language] ||
                    playground.props.translations?.description?.['en'] ||
                    playground.props.description
                  }
                  variant={playground.props.variant}
                  locked={playground.props.locked}
                  tooltipText={
                    playground.props.locked
                      ? playground.props.tooltipText || t('common.lockedTooltip')
                      : undefined
                  }
                  buttonURL={playground.props.buttonURL}
                  buttonText={playground.props.buttonText}
                  buttonType={playground.props.buttonType}
                  newTab={playground.props.newTab}
                  onClick={() => {
                    if (playground.props.locked) return;

                    if (playground.props.newTab) {
                      window.open(playground.props.buttonURL, '_blank');
                    } else {
                      navigate(playground.props.buttonURL);
                    }
                  }}
                />
              ))}
            </Carousel>
          </WhiteContainer>
        </Grid>

        {/* AI Ecosystems section */}
        <Grid item xs={12} mb={4}>
          <WhiteContainer
            title={t('home.techCompanies.title', 'AI ecosystems provided by leading tech firms')}
            subtitle={t(
              'home.techCompanies.subtitle',
              'Discover and access AI tools from leading tech companies.'
            )}
            showNavigation={true}
            variant="transparent"
          >
            <Carousel
              swiperProps={{
                slidesPerView: 'auto',
                spaceBetween: 24,
              }}
              breakpoints={{
                320: { slidesPerView: 2, spaceBetween: 24 },
                768: { slidesPerView: 4, spaceBetween: 24 },
                1024: { slidesPerView: 5, spaceBetween: 24 },
                1440: { slidesPerView: 5, spaceBetween: 24 },
              }}
            >
              {filteredCompanies.map((company) => (
                <CardWithIcon
                  key={company.props.id}
                  image={company.props.image}
                  title={company.props.title}
                  description=""
                  locked={company.props.locked}
                  tooltipText={
                    company.props.locked
                      ? company.props.tooltipText || t('common.lockedTooltip')
                      : undefined
                  }
                  buttonType={company.props.buttonType}
                  variant={company.props.variant}
                  buttonURL={company.props.buttonURL}
                  class={company.props.class}
                  onClick={() => {
                    if (company.props.locked) return;
                    if (company.props.newTab) {
                      window.open(company.props.buttonURL, '_blank');
                    } else {
                      navigate(company.props.buttonURL);
                    }
                  }}
                />
              ))}
            </Carousel>
          </WhiteContainer>
        </Grid>

        {/* AI Chat Section */}
        {isAiChatEnabled && (
          <Grid item xs={12}>
            <WhiteContainer
              title="Copilot Playground"
              subtitle={t('home.aiChat.subtitle')}
              showNavigation={false}
              variant="transparent"
            >
              <Box sx={{ marginTop: '20px' }}>
                <AiChat/>
              </Box>
            </WhiteContainer>
          </Grid>
        )}
      </Grid>

      {/* Platform Video Modal */}
      <AIModal
        open={isVideoModalOpen}
        onClose={() => setIsVideoModalOpen(false)}
        title={t('home.introVideo.title', 'Platform Introduction Video')}
        content={`<div><iframe src="${videoUrl}" width="100%" height="auto" frameborder="0" allowfullscreen></iframe></div>`}
        showCloseButton={true}
      />
    </Container>
  );
};

CourseCardContent.propTypes = {
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  buttonURL: PropTypes.string,
  newTab: PropTypes.bool,
  buttonText: PropTypes.string,
  buttonType: PropTypes.string,
  imageSrc: PropTypes.string,
};

const HomePage = () => {
  return (
    <Suspense
      fallback={
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: 'calc(100vh - 64px)', // Subtracting header height
          }}
        >
          <CircularProgress />
        </Box>
      }
    >
      <HomePageContent />
    </Suspense>
  );
};

export default HomePage;
