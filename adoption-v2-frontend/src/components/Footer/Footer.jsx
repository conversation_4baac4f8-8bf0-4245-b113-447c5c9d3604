import { Suspense, useState, useEffect } from 'react';
import { Box, Container, CircularProgress, Typography } from '@mui/material';
import LinkedInIcon from '@mui/icons-material/LinkedIn';
import EmailIcon from '@mui/icons-material/Email';
import { useTranslation } from 'react-i18next';
import { useSelector, useDispatch } from 'react-redux';
import './Footer.scss';
import TrainingProgress from '../TrainingProgress/TrainingProgress';
import AIModal from '../AIModal/AIModal';
import FormModal from '../FormModal/FormModal';
import { updateGlobalTrainingProgress } from '../../redux/features/training/trainingSlice';
import { useGetJourneysQuery, useGetJourneyTrackingQuery } from '../../redux/services/journey-api';
import FormRender from '../FormRender/FormRender';
import { useFormByIdQuery } from '../../redux/services/form-service';
import { useSubmitFormResponseMutation } from '../../redux/services/form-response';
import useUpdateJourneyTracking from '../../domains/journey/utils/updateJourneyTracking';
import { IDEA_FORM_ID } from '../../constants/form-constants';
import { developmentLogs } from '@/utils/developmentLogs';

// const IDEA_FORM_ID = '67b324528b6aa4de9992145c';

const FooterContent = () => {
  const { t, i18n } = useTranslation();
  const dispatch = useDispatch();
  const showTrainingProgress = useSelector((state) => state.training.showTrainingProgress);
  const globalTrainingProgress = useSelector((state) => state.training.globalTrainingProgress);
  const user = useSelector((state) => state.auth.user);
  const selectedLevel = useSelector((state) => state.training.selectedLevel);

  // Modal states
  const [isContentModalOpen, setIsContentModalOpen] = useState(false);
  const [ideationModalData, setIdeationModalData] = useState(null); // Use object instead of boolean

  // Ideation Form states
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({});
  const [formErrors, setFormErrors] = useState({});
  const [isSubmittingForm] = useState(false);

  // RTK mutation hook for submitting form response
  const [submitFormResponse] = useSubmitFormResponseMutation();

  // Hook for updating journey tracking
  const { updateJourneyTrackingCard } = useUpdateJourneyTracking();

  // Get form ID
  const formId = ideationModalData?.formId || IDEA_FORM_ID;

  // Fetch ideation form - useFormByIdQuery
  const { data: ideationFormData } = useFormByIdQuery(
    {
      formId: formId,
      lang: i18n.language || user?.onboarding?.language?.slug || 'en',
    },
    {
      skip: !ideationModalData, // Don't fetch data if modal is not open
      refetchOnMountOrArgChange: true,
    }
  );

  // Fetch journey and tracking data
  const { data: journeyData, refetch: refetchJourneyData } = useGetJourneysQuery(
    {
      function: user?.onboarding?.function_label?.slug,
      levels: user?.journeyLevel?.name,
      managementRole: user?.onboarding?.management_role_label.slug,
    },
    {
      skip: !user?.onboarding?.function_label?.slug || !user?.journeyLevel?.name,
    }
  );

  const { data: trackingData, refetch: refetchTracking } = useGetJourneyTrackingQuery(user?._id, {
    skip: !user?._id || !user,
    refetchOnMountOrArgChange: true,
  });

  // Detect manual updates to journey tracking and refetch
  useEffect(() => {
    // Function to check for changes in localStorage
    const checkForTrackingUpdates = () => {
      const lastUpdate = localStorage.getItem('journey_tracking_updated');
      if (lastUpdate) {
        // Change detected, refetch data
        refetchTracking();
        // Clear single-use flag after operation
        localStorage.removeItem('journey_tracking_updated');
      }
    };

    // Check
    const intervalId = setInterval(checkForTrackingUpdates, 3000);

    // Cleanup
    return () => {
      clearInterval(intervalId);
    };
  }, [refetchTracking]);

  // Function to calculate and update training progress values
  const updateTrainingProgressValues = () => {
    if (!journeyData || !trackingData || !user?.journeyLevel?.name) return;

    // Use user's actual level - instead of selected level
    const userLevel = user.journeyLevel.name.toLowerCase();

    const completedJourneys = trackingData[userLevel] || [];
    let totalPercent = 0;
    let completedCount = 0;

    // Find first incomplete journey (current step)
    const currentJourney = journeyData.find((journey) => {
      return journey._id && !completedJourneys.includes(journey._id);
    });

    journeyData.forEach((journey) => {
      if (journey._id && completedJourneys.includes(journey._id)) {
        completedCount++;
        if (typeof journey.percent === 'number') {
          totalPercent += journey.percent;
        }
      }
    });

    // Calculate next journey and determine button type
    let buttonType = '';
    let modalContent = null;
    let nextStepUrl = '/training/next-step';
    let currentStepTitle = t('training.progress.allStepsCompleted', 'All steps completed');

    if (currentJourney) {
      const journeyType = currentJourney.journeyType;
      const language = i18n.language || user?.onboarding?.language || 'english';
      const translationKey = language === 'de' ? 'german' : 'english';
      const selectedTranslation =
        currentJourney.translations?.[translationKey] || currentJourney.translations?.english || {};

      if (journeyType === 'Course' || journeyType === 'course') {
        buttonType = 'Course';
        modalContent = {
          courseId: currentJourney.course,
          cardId: currentJourney._id,
          title: selectedTranslation.title || currentJourney.title,
        };
      } else if (journeyType === 'Content' || journeyType === 'content') {
        buttonType = 'Content';
        modalContent = {
          title: selectedTranslation.title || currentJourney.title,
          content: selectedTranslation.content || currentJourney.content,
          cardId: currentJourney._id,
        };
      } else if (journeyType === 'Ideation Project') {
        buttonType = 'Ideation Project';
        modalContent = {
          cardId: currentJourney._id,
          ideationFormId: currentJourney.ideation || IDEA_FORM_ID,
          title: selectedTranslation.title || currentJourney.title,
        };
      } else if (journeyType === 'Usecases') {
        buttonType = 'Usecases';
        modalContent = {
          useCaseId: currentJourney.selectedFunctionSpecUseCases,
          cardId: currentJourney._id,
          title: selectedTranslation.title || currentJourney.title,
        };
      } else if (journeyType === 'App Creator') {
        buttonType = 'App Creator';
        modalContent = {
          cardId: currentJourney._id,
          title: selectedTranslation.title || currentJourney.title,
          buttonURL: currentJourney.buttonURL,
        };
      } else if (journeyType === 'Workflow Creator') {
        buttonType = 'Workflow Creator';
        modalContent = {
          cardId: currentJourney._id,
          title: selectedTranslation.title || currentJourney.title,
          buttonURL: currentJourney.buttonURL,
        };
      } else if (journeyType === 'GenAI Playground') {
        buttonType = 'GenAI Playground';
        modalContent = {
          cardId: currentJourney._id,
          title: selectedTranslation.title || currentJourney.title,
          buttonURL: currentJourney.buttonURL,
        };
      }

      // Currentjourney title translation
      currentStepTitle = selectedTranslation.title || currentJourney.title;
    } else if (journeyData.length > 0 && completedCount === journeyData.length) {
      // All steps completed
      totalPercent = 100;
      buttonType = '';
      modalContent = null;
    }

    // Update Redux store
    dispatch(
      updateGlobalTrainingProgress({
        progress: totalPercent,
        completedSteps: completedCount,
        totalSteps: journeyData.length,
        currentStepTitle: currentStepTitle,
        buttonType,
        modalContent,
        nextStepUrl,
      })
    );
  };

  // Update TrainingProgress values when data is fetched or changed
  useEffect(() => {
    updateTrainingProgressValues();
  }, [journeyData, trackingData, selectedLevel, i18n.language]);

  // Check completed cards when loading
  useEffect(() => {
    // Check if there is a completed card in localStorage
    const completedCardId = localStorage.getItem('tracking_completed_card');

    if (completedCardId && user?._id && trackingData) {
      // Verify
      const userLevel = user.journeyLevel.name.toLowerCase();
      const completedJourneys = trackingData[userLevel] || [];

      if (completedJourneys.includes(completedCardId)) {
        // Verify completed, trigger special event
        setTimeout(() => {
          // Update TrainingProgress values and clear localStorage
          updateTrainingProgressValues();
        }, 1000);
      }

      // Clear localStorage in all cases
      localStorage.removeItem('tracking_completed_card');
    }
  }, [user, trackingData, updateTrainingProgressValues]);

  // Update TrainingProgress values when usecase is generated
  useEffect(() => {
    // Define event listener
    const handleUpdateTrainingProgress = () => {
      // Fetch data with a delay - this ensures other operations are complete
      setTimeout(() => {
        // First refresh tracking data, then update TrainingProgress values
        Promise.all([refetchTracking(), refetchJourneyData()]).then(
          ([trackingResult, journeyResult]) => {
            // Update TrainingProgress values with the latest data
            if (trackingResult.data && journeyResult.data) {
              // Update state with the latest data
              const userLevel = user?.journeyLevel?.name.toLowerCase();
              const completedJourneys = trackingResult.data[userLevel] || [];
              let totalPercent = 0;
              let completedCount = 0;

              // Find first incomplete journey (current step)
              const currentJourney = journeyResult.data.find((journey) => {
                return journey._id && !completedJourneys.includes(journey._id);
              });

              journeyResult.data.forEach((journey) => {
                if (journey._id && completedJourneys.includes(journey._id)) {
                  completedCount++;
                  if (typeof journey.percent === 'number') {
                    totalPercent += journey.percent;
                  }
                }
              });

              // Calculate next journey and determine button type
              let buttonType = '';
              let modalContent = null;
              let nextStepUrl = '/training/next-step';
              let currentStepTitle = t(
                'training.progress.allStepsCompleted',
                'All steps completed'
              );

              if (currentJourney) {
                const journeyType = currentJourney.journeyType;
                const language = i18n.language || user?.onboarding?.language || 'english';
                const translationKey = language === 'de' ? 'german' : 'english';
                const selectedTranslation =
                  currentJourney.translations?.[translationKey] ||
                  currentJourney.translations?.english ||
                  {};

                if (journeyType === 'Course' || journeyType === 'course') {
                  buttonType = 'Course';
                  modalContent = {
                    courseId: currentJourney.course,
                    cardId: currentJourney._id,
                    title: selectedTranslation.title || currentJourney.title,
                  };
                } else if (journeyType === 'Content' || journeyType === 'content') {
                  buttonType = 'Content';
                  modalContent = {
                    title: selectedTranslation.title || currentJourney.title,
                    content: selectedTranslation.content || currentJourney.content,
                    cardId: currentJourney._id,
                  };
                } else if (journeyType === 'Ideation Project') {
                  buttonType = 'Ideation Project';
                  modalContent = {
                    cardId: currentJourney._id,
                    ideationFormId: currentJourney.ideation || IDEA_FORM_ID,
                    title: selectedTranslation.title || currentJourney.title,
                  };
                } else if (journeyType === 'Usecases') {
                  buttonType = 'Usecases';
                  modalContent = {
                    useCaseId: currentJourney.selectedFunctionSpecUseCases,
                    cardId: currentJourney._id,
                    title: selectedTranslation.title || currentJourney.title,
                  };
                } else if (journeyType === 'App Creator') {
                  buttonType = 'App Creator';
                  modalContent = {
                    cardId: currentJourney._id,
                    title: selectedTranslation.title || currentJourney.title,
                    buttonURL: currentJourney.buttonURL,
                  };
                } else if (journeyType === 'Workflow Creator') {
                  buttonType = 'Workflow Creator';
                  modalContent = {
                    cardId: currentJourney._id,
                    title: selectedTranslation.title || currentJourney.title,
                    buttonURL: currentJourney.buttonURL,
                  };
                } else if (journeyType === 'GenAI Playground') {
                  buttonType = 'GenAI Playground';
                  modalContent = {
                    cardId: currentJourney._id,
                    title: selectedTranslation.title || currentJourney.title,
                    buttonURL: currentJourney.buttonURL,
                  };
                }

                // Currentjourney title translation
                currentStepTitle = selectedTranslation.title || currentJourney.title;
              } else if (
                journeyResult.data.length > 0 &&
                completedCount === journeyResult.data.length
              ) {
                // All steps completed
                totalPercent = 100;
                buttonType = '';
                modalContent = null;
              }

              // Update Redux store
              dispatch(
                updateGlobalTrainingProgress({
                  progress: totalPercent,
                  completedSteps: completedCount,
                  totalSteps: journeyResult.data.length,
                  currentStepTitle,
                  buttonType,
                  modalContent,
                  nextStepUrl,
                })
              );
            }
          }
        );
      }, 500); // 500ms delay to ensure other operations are complete
    };

    // Add event listener
    window.addEventListener('update_training_progress', handleUpdateTrainingProgress);

    // Cleanup: remove event listener when component unmounts
    return () => {
      window.removeEventListener('update_training_progress', handleUpdateTrainingProgress);
    };
  }, [dispatch, refetchTracking, refetchJourneyData, user, t, i18n.language]);

  // Modal handlers
  const handleModalClose = (result) => {
    setIsContentModalOpen(false);

    // If content is completed, update training progress values
    if (result && result.completed) {
      refetchTracking();
      refetchJourneyData();
      updateTrainingProgressValues();
    }
  };

  // Close ideation form modal
  const handleIdeationModalClose = () => {
    setIdeationModalData(null); // Clear modal data
    setActiveStep(0);
    setFormData({});
    setFormErrors({});
  };

  // Handle form changes
  const handleFormChange = (newData) => {
    // Form change handling...
    setFormData(newData);
  };

  // Ideation form submission
  const handleIdeationFormSubmit = async () => {
    try {
      // Get form fields directly from data.fields
      const formFields = ideationFormData?.data?.fields || [];

      // Add all filled fields to responses
      const responses = formFields
        .filter(
          (field) =>
            formData[field.name] !== undefined &&
            formData[field.name] !== null &&
            formData[field.name] !== ''
        )
        .map((field) => ({
          fieldId: field._id,
          name: field.label.toLowerCase(),
          type: field.type,
          value: formData[field.name],
        }));

      // Determine source name based on journey level
      const userLevel = user?.journeyLevel?.name?.toLowerCase() || 'unknown';
      const sourceType = ideationModalData?.cardId ? `${userLevel}-journey` : 'footer-ideation';

      // Prepare form submission data
      const requestData = {
        formId: formId,
        formType: 'ideation',
        title: ideationFormData?.data?.title || 'Ideation Project',
        description: ideationFormData?.data?.description || '',
        responses,
        source: sourceType,
        sourceDetails: {
          cardId: ideationModalData?.cardId || null,
          journeyLevel: userLevel,
        },
      };

      // Submit form
      const response = await submitFormResponse(requestData).unwrap();

      if (response && response.status === 'success') {
        // Update journey tracking
        // Mark journey card as completed using cardId from ideationModalData
        if (ideationModalData?.cardId) {
          // Update journey card
          await updateJourneyTrackingCard({
            userId: user?._id,
            journeyTrackingData: trackingData,
            userLevel: user?.journeyLevel?.name.toLowerCase(),
            cardId: ideationModalData.cardId,
          });
        }

        // Close modal and reset state
        handleIdeationModalClose();

        // Refresh data
        refetchTracking();
        refetchJourneyData();
        updateTrainingProgressValues();

        return true;
      } else {
        handleIdeationModalClose();
        return false;
      }
    } catch (error) {
      developmentLogs('Ideation form submission error:', error);
      handleIdeationModalClose();
      return false;
    }
  };

  return (
    <>
      {showTrainingProgress && (
        <TrainingProgress
          progress={globalTrainingProgress.progress}
          totalSteps={globalTrainingProgress.totalSteps}
          completedSteps={globalTrainingProgress.completedSteps}
          currentStepTitle={globalTrainingProgress.currentStepTitle}
          nextStepUrl={globalTrainingProgress.nextStepUrl}
          buttonType={globalTrainingProgress.buttonType}
          modalContent={globalTrainingProgress.modalContent}
          // Modal handlers
          setIsContentModalOpen={setIsContentModalOpen}
          setIsIdeationModalOpen={setIdeationModalData} // Use new state with object type
        />
      )}

      {/* AIModal (Content or MODAL type) */}
      {isContentModalOpen && globalTrainingProgress.modalContent && (
        <AIModal
          open={isContentModalOpen}
          onClose={handleModalClose}
          title={globalTrainingProgress.modalContent?.title || ''}
          content={globalTrainingProgress.modalContent?.content || ''}
          cardId={globalTrainingProgress.modalContent?.cardId}
          refetchTrackingData={refetchTracking}
          refetchJourneyData={refetchJourneyData}
          updateTrainingProgressValues={updateTrainingProgressValues}
        />
      )}

      {/* Ideation Form Modal */}
      {ideationModalData && (
        <FormModal
          open={!!ideationModalData}
          onClose={handleIdeationModalClose}
          title={t('ideation.form.title', 'Share Your Idea')}
          onSubmit={handleIdeationFormSubmit}
          maxWidth="md"
          activeStep={activeStep}
          steps={[
            {
              title: t('ideation.form.steps.describe.title', 'Describe your idea'),
              label: t('ideation.form.steps.describe.label', 'Description'),
              onChange: handleFormChange,
              content: (
                <FormRender
                  hideTitle={true}
                  hideDescription={false}
                  formTitle={t('ideation.form.steps.describe.title', 'Describe your idea')}
                  formData={{
                    _id: ideationFormData?.data?._id || ideationFormData?._id || 'idea-form',
                    title:
                      ideationFormData?.data?.title ||
                      ideationFormData?.title ||
                      t('ideation.form.title', 'Share Your Idea'),
                    description:
                      ideationFormData?.data?.description || ideationFormData?.description || '',
                    type: 'form',
                    topics:
                      (ideationFormData?.data?.topics || ideationFormData?.topics || [])
                        .map((topic) => ({
                          ...topic,
                          fields:
                            topic.fields?.filter((field) => {
                              const name = field.name?.toLowerCase().trim();
                              return (
                                name === 'title' ||
                                name === 'description' ||
                                name.includes('describe') ||
                                name.includes('title')
                              );
                            }) || [],
                        }))
                        .filter((topic) => topic.fields?.length > 0) || [],
                    fields:
                      (ideationFormData?.data?.fields || ideationFormData?.fields || []).filter(
                        (field) => {
                          const name = field.name?.toLowerCase().trim();
                          return (
                            name === 'title' ||
                            name === 'description' ||
                            name.includes('describe') ||
                            name.includes('title')
                          );
                        }
                      ) || [],
                  }}
                  onChange={handleFormChange}
                  values={formData}
                  errors={formErrors}
                  disabled={isSubmittingForm}
                />
              ),
            },
            {
              title: t('ideation.form.steps.classify.title', 'Classify your idea'),
              label: t('ideation.form.steps.classify.label', 'Classification'),
              onChange: handleFormChange,
              content: (
                <FormRender
                  hideTitle={true}
                  hideDescription={true}
                  formTitle={t('ideation.form.steps.classify.title', 'Classify your idea')}
                  formData={{
                    _id: ideationFormData?.data?._id || ideationFormData?._id || 'idea-form',
                    title:
                      ideationFormData?.data?.title ||
                      ideationFormData?.title ||
                      t('ideation.form.title', 'Share Your Idea'),
                    description: '',
                    type: 'form',
                    topics:
                      (ideationFormData?.data?.topics || ideationFormData?.topics || [])
                        .map((topic) => ({
                          ...topic,
                          fields:
                            topic.fields?.filter((field) => {
                              const name = field.name?.toLowerCase().trim();
                              return (
                                name !== 'title' &&
                                name !== 'description' &&
                                !name.includes('describe') &&
                                !name.includes('title')
                              );
                            }) || [],
                        }))
                        .filter((topic) => topic.fields?.length > 0) || [],
                    fields:
                      (ideationFormData?.data?.fields || ideationFormData?.fields || []).filter(
                        (field) => {
                          const name = field.name?.toLowerCase().trim();
                          return (
                            name !== 'title' &&
                            name !== 'description' &&
                            !name.includes('describe') &&
                            !name.includes('title')
                          );
                        }
                      ) || [],
                  }}
                  onChange={handleFormChange}
                  values={formData}
                  errors={formErrors}
                  disabled={isSubmittingForm}
                />
              ),
            },
          ]}
          showReview={true}
          formErrors={formErrors}
          loading={isSubmittingForm}
          values={formData}
          onChange={handleFormChange}
        >
          {isSubmittingForm && (
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'rgba(255, 255, 255, 0.7)',
                zIndex: 2,
              }}
            >
              <Box sx={{ textAlign: 'center' }}>
                <CircularProgress size={60} />
                <Typography sx={{ mt: 2 }}>{t('common.loading', 'Loading...')}</Typography>
              </Box>
            </Box>
          )}
        </FormModal>
      )}

      <Box component="footer" className="footer">
        <Container maxWidth="lg">
          <Box className="footer-container">
            <Box className="footer-links left">
              <a href="mailto:<EMAIL>" className="footer-link">
                <EmailIcon />
                <EMAIL>
              </a>

              <a
                href="https://www.linkedin.com/company/ai-business-school/"
                target="_blank"
                rel="noopener noreferrer"
                className="footer-link"
              >
                <LinkedInIcon />
                LinkedIn
              </a>
            </Box>

            <Box className="footer-links right">
              <a href="/privacy-policy" className="footer-link">
                {t('footer.privacyPolicy')}
              </a>
            </Box>
          </Box>
        </Container>
      </Box>
    </>
  );
};

const Footer = () => {
  return (
    <Suspense
      fallback={
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '64px',
            bgcolor: 'background.paper',
            borderTop: '1px solid',
            borderColor: 'divider',
          }}
        >
          <CircularProgress size={24} />
        </Box>
      }
    >
      <FooterContent />
    </Suspense>
  );
};

export default Footer;
