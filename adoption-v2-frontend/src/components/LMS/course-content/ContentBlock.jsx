import React, { useState, useEffect, useRef, useMemo } from 'react';
import {
  Box,
  Typography,
  Button,
  CircularProgress,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  RadioGroup,
  Radio,
  FormControlLabel,
  Checkbox,
  FormGroup,
  Card,
  CardContent,
  Grid,
  IconButton,
  LinearProgress,
  FormHelperText,
} from '@mui/material';
import { AttachFile, Delete } from '@mui/icons-material';
import { toast } from 'react-toastify';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';

import Quiz from '../Quiz';
import EnhancedVideoPlayer from '../EnhancedVideoPlayer';
// Define VideoPlayer alias
const VideoPlayer = EnhancedVideoPlayer;
import YouTube from 'react-youtube';
import axios from 'axios';
import SingleApp from '../../../pages/SingleApp';
import ImageUsecase from '../../../domains/apps/components/usecases/ImageUsecase';
import DallEPlayground from '../../../components/DallEPlayground/DallEPlayground';
import CopilotChatPlayground from '../../../components/CopilotChatPlayground/CopilotChatPlayground';
import HeygenVideoCreator from '../../../components/HeygenVideoCreator';
import DownloadIcon from '@mui/icons-material/Download';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import ZoomOutIcon from '@mui/icons-material/ZoomOut';
import FullscreenIcon from '@mui/icons-material/Fullscreen';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import './ContentBlock.scss';
import { CDS_API_URL, CDS_API_KEY } from '../../../config-global';
import { useSubmitFormResponseMutation, uploadFile } from '../../../redux/services/form-response';
import { useFormsQuery } from '../../../redux/services/form-service';
import { useSelector } from 'react-redux';
import { selectCurrentToken } from '../../../redux/features/auth/authSlice';

// PDF.js imports
import * as pdfjsLib from 'pdfjs-dist';

// PDF worker settings
pdfjsLib.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js`;

// PDF viewer component
const PdfViewer = ({ pdfUrl, title, description }) => {
  const [numPages, setNumPages] = useState(null);
  const [pdfError, setPdfError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [pdfDoc, setPdfDoc] = useState(null);
  const [scale, setScale] = useState(1.0);
  const [currentPage, setCurrentPage] = useState(1);
  const canvasContainerRef = useRef(null);
  const pdfContainerRef = useRef(null);
  const { t } = useTranslation();

  // Load PDF
  const loadPDF = async (url) => {
    try {
      setIsLoading(true);

      // Create PDF loading task
      const loadingTask = pdfjsLib.getDocument({
        url: url,
        cMapUrl: 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/cmaps/',
        cMapPacked: true,
      });

      // Loading progress status
      loadingTask.onProgress = function (progress) {
        return (
          <LinearProgress value={(progress.loaded / progress.total) * 100} variant="determinate" />
        );
      };

      const pdf = await loadingTask.promise;

      // PDF loaded, update states
      setPdfDoc(pdf);
      setNumPages(pdf.numPages);
      setCurrentPage(1);
      setIsLoading(false);
      setPdfError(null);
    } catch (err) {
      console.error('PDF loading error:', err.message);
      setPdfError(err);
      setIsLoading(false);
    }
  };

  // Render PDF pages
  const renderAllPages = async () => {
    if (!pdfDoc || !canvasContainerRef.current) return;

    try {
      // Clear content
      canvasContainerRef.current.innerHTML = '';

      // Render each page
      for (let pageNum = 1; pageNum <= numPages; pageNum++) {
        const page = await pdfDoc.getPage(pageNum);

        // Create page container
        const pageContainer = document.createElement('div');
        pageContainer.className = 'pdf-page-container';
        pageContainer.setAttribute('data-page-number', pageNum);

        // Create page number
        const pageNumberElement = document.createElement('div');
        pageNumberElement.className = 'pdf-page-number';
        pageNumberElement.textContent = t('courseContent.pdfPageOf', { pageNum, numPages });
        pageContainer.appendChild(pageNumberElement);

        // Create canvas
        const canvas = document.createElement('canvas');
        canvas.className = 'pdf-canvas';
        pageContainer.appendChild(canvas);

        // Add container
        canvasContainerRef.current.appendChild(pageContainer);

        // Render PDF page
        const context = canvas.getContext('2d', { alpha: false });
        const viewport = page.getViewport({
          scale: scale,
          rotation: 0,
          offsetX: 0,
          offsetY: 0,
        });

        // Adjust canvas size according to viewport - use 2x for high resolution
        const pixelRatio = window.devicePixelRatio || 1;
        canvas.width = viewport.width * pixelRatio;
        canvas.height = viewport.height * pixelRatio;

        // Dynamically adjust canvas style according to scale value - actual size instead of full width
        canvas.style.width = `${viewport.width}px`;
        canvas.style.height = `${viewport.height}px`;
        canvas.style.maxWidth = 'none';

        // Scale context for high resolution
        context.scale(pixelRatio, pixelRatio);

        // Render page
        await page.render({
          canvasContext: context,
          viewport: viewport,
          enableWebGL: true, // WebGL acceleration
          annotationMode: 2, // ENABLE_FORMS = 2
          intent: 'print',
        }).promise;
      }
    } catch (err) {
      console.error('PDF pages rendering error:', err);
      setPdfError(err);
    }
  };

  // Zoom function
  const handleZoom = (delta) => {
    setScale((prevScale) => {
      // Zoom step
      const zoomStep = 0.25;

      // Set zoom change and limits
      let newScale;
      if (delta < 0) {
        // Zoom out - decrease by 0.25, minimum 0.5
        newScale = Math.max(0.5, prevScale - zoomStep);
      } else {
        // Zoom in - increase by 0.25, maximum 4.0
        newScale = Math.min(4.0, prevScale + zoomStep);
      }

      // Complete zoom change
      return Math.round(newScale * 100) / 100; // Round to two decimal places
    });
  };

  // Fullscreen function
  const handleFullscreen = () => {
    if (pdfContainerRef.current) {
      if (
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement
      ) {
        // Compatible exitFullscreen call for different browsers
        const exitMethod =
          document.exitFullscreen ||
          document.webkitExitFullscreen ||
          document.mozCancelFullScreen ||
          document.msExitFullscreen;

        if (exitMethod) {
          exitMethod.call(document).catch((err) => console.error('Fullscreen exit error:', err));
        }
      } else {
        // Compatible requestFullscreen call for different browsers
        const element = pdfContainerRef.current;
        const requestMethod =
          element.requestFullscreen ||
          element.webkitRequestFullscreen ||
          element.mozRequestFullScreen ||
          element.msRequestFullscreen;

        if (requestMethod) {
          requestMethod.call(element).catch((err) => console.error('Fullscreen error:', err));
        } else {
          console.warn('Browser does not support fullscreen mode');
        }
      }
    }
  };

  // Go to previous page
  const handlePrevPage = () => {
    if (currentPage > 1) {
      const newPage = currentPage - 1;
      setCurrentPage(newPage);
      scrollToPage(newPage);
    }
  };

  // Go to next page
  const handleNextPage = () => {
    if (currentPage < numPages) {
      const newPage = currentPage + 1;
      setCurrentPage(newPage);
      scrollToPage(newPage);
    }
  };

  // Scrolls to a specific page
  const scrollToPage = (pageNum) => {
    const pageElem = canvasContainerRef.current?.querySelector(`[data-page-number="${pageNum}"]`);
    if (pageElem) {
      pageElem.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  // Load PDF when PDF URL changes
  useEffect(() => {
    if (pdfUrl) {
      loadPDF(pdfUrl);
    }
  }, [pdfUrl]);

  // Render pages when PDF is loaded and scale changes
  useEffect(() => {
    if (pdfDoc) {
      renderAllPages();
    }
  }, [pdfDoc, scale]);

  // Listen to scroll event
  useEffect(() => {
    const handleScroll = () => {
      if (!canvasContainerRef.current) return;

      // Detect the page visible on screen
      const containerRect = canvasContainerRef.current.getBoundingClientRect();
      const pageElements = canvasContainerRef.current.querySelectorAll('.pdf-page-container');

      // Find the most visible page
      let mostVisiblePage = 1;
      let maxVisibleArea = 0;

      pageElements.forEach((pageElem) => {
        const pageRect = pageElem.getBoundingClientRect();
        const pageNum = parseInt(pageElem.getAttribute('data-page-number'));

        // Calculate intersection area between page and container
        const visibleTop = Math.max(pageRect.top, containerRect.top);
        const visibleBottom = Math.min(pageRect.bottom, containerRect.bottom);
        const visibleHeight = Math.max(0, visibleBottom - visibleTop);

        if (visibleHeight > maxVisibleArea) {
          maxVisibleArea = visibleHeight;
          mostVisiblePage = pageNum;
        }
      });

      if (mostVisiblePage !== currentPage) {
        setCurrentPage(mostVisiblePage);
      }
    };

    const container = canvasContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
    }

    return () => {
      if (container) {
        container.removeEventListener('scroll', handleScroll);
      }
    };
  }, [currentPage]);

  return (
    <Box sx={{ p: 2 }}>
      {title && (
        <Typography variant="h5" gutterBottom>
          {title}
        </Typography>
      )}
      {description && (
        <Typography variant="body1" color="text.secondary" gutterBottom>
          {description}
        </Typography>
      )}

      {pdfUrl ? (
        <Box
          ref={pdfContainerRef}
          sx={{
            mt: 3,
            width: '100%',
            height: '80vh',
            border: '1px solid #ddd',
            borderRadius: 1,
            overflow: 'hidden',
            position: 'relative',
            bgcolor: 'background.paper',
            '.pdf-controls': {
              display: 'flex',
              alignItems: 'center',
              padding: '8px 16px',
              borderBottom: '1px solid #ddd',
              backgroundColor: '#f5f5f5',
            },
            '.pdf-control-button': {
              mx: 0.5,
            },
            '.page-info': {
              mx: 2,
              fontSize: '0.875rem',
            },
            '.zoom-info': {
              mx: 2,
              fontSize: '0.875rem',
            },
            '.controls-divider': {
              height: '24px',
              width: '1px',
              backgroundColor: '#ddd',
              mx: 2,
            },
            '.canvas-container': {
              height: 'calc(100% - 48px)',
              overflowY: 'auto',
              padding: '16px',
            },
            '.pdf-page-container': {
              position: 'relative',
              marginBottom: '24px',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
            },
            '.pdf-page-number': {
              fontSize: '0.75rem',
              color: '#666',
              marginBottom: '8px',
            },
            '.pdf-canvas': {
              border: '1px solid #ddd',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            },
          }}
        >
          {isLoading ? (
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100%',
              }}
            >
              <CircularProgress />
            </Box>
          ) : pdfError ? (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100%',
                p: 3,
                textAlign: 'center',
              }}
            >
              <Typography color="error" gutterBottom>
                {t('courseContent.pdf.errorLoading')}
              </Typography>
              <Button
                variant="outlined"
                color="primary"
                onClick={() => window.open(pdfUrl, '_blank')}
              >
                {t('courseContent.pdf.openInNewTab')}
              </Button>
            </Box>
          ) : (
            <>
              <div className="pdf-controls">
                <IconButton
                  onClick={handlePrevPage}
                  className="pdf-control-button"
                  disabled={currentPage <= 1}
                >
                  <ArrowBackIcon />
                </IconButton>

                <Typography className="page-info">
                  {currentPage} / {numPages}
                </Typography>

                <IconButton
                  onClick={handleNextPage}
                  className="pdf-control-button"
                  disabled={currentPage >= numPages}
                >
                  <ArrowForwardIcon />
                </IconButton>

                <div className="controls-divider"></div>

                <IconButton onClick={() => handleZoom(-1)} className="pdf-control-button">
                  <ZoomOutIcon />
                </IconButton>

                <Typography className="zoom-info">{Math.round(scale * 100)}%</Typography>

                <IconButton onClick={() => handleZoom(1)} className="pdf-control-button">
                  <ZoomInIcon />
                </IconButton>

                <IconButton onClick={handleFullscreen} className="pdf-control-button">
                  <FullscreenIcon />
                </IconButton>

                <Box sx={{ marginLeft: 'auto' }}>
                  <Button
                    variant="outlined"
                    color="primary"
                    startIcon={<DownloadIcon />}
                    onClick={() => window.open(pdfUrl, '_blank')}
                  >
                    {t('courseContent.pdf.download')}
                  </Button>
                </Box>
              </div>

              <div className="canvas-container" ref={canvasContainerRef}></div>
            </>
          )}
        </Box>
      ) : (
        <Box
          sx={{
            mt: 2,
            p: 3,
            border: '1px dashed',
            borderColor: 'divider',
            borderRadius: 1,
            textAlign: 'center',
          }}
        >
          <Typography variant="body1" color="text.secondary">
            {t('courseContent.pdf.notFound')}
          </Typography>
        </Box>
      )}
    </Box>
  );
};

const ContentBlock = ({
  block,
  courseId,
  chapterIndex,
  topicIndex,
  onQuizComplete,
  onQuizScoreChange,
  isTopicCompleted,
  onVideoEnded,
  onTimeUpdate,
  onUsecaseGenerate,
  onContentCompleted,
  onFormSubmit,
}) => {
  const [useCaseLoading, setUseCaseLoading] = useState(false);
  const [useCaseError, setUseCaseError] = useState(null);
  const [useCase, setUseCase] = useState(null);
  const [pdfError, setPdfError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [pdfBase64, setPdfBase64] = useState(null);
  const [isContentCompleted, setIsContentCompleted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isProcessing = useRef(false); // Added useRef to track processing state

  // Add i18n hook for translation
  const { t, i18n } = useTranslation();
  const currentLanguage = i18n.language || 'en';
  const convertedLanguage =
    currentLanguage === 'en' ? 'english' : currentLanguage === 'de' ? 'german' : '';

  // Get token from Redux
  const token = useSelector(selectCurrentToken);

  // Form states
  const [formValues, setFormValues] = useState({});
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [formSubmitting, setFormSubmitting] = useState(false);
  const [formErrors, setFormErrors] = useState({});
  const [formId, setFormId] = useState(null);
  const [uploadingFiles, setUploadingFiles] = useState({});
  const [uploadErrors, setUploadErrors] = useState({});

  // Define Add Form Response Mutation
  const [submitFormResponse, { isLoading: isSubmittingForm }] = useSubmitFormResponseMutation();

  // Query hook to get form data
  const { data: formData } = useFormsQuery(
    { formId, language: currentLanguage },
    { skip: !formId }
  );

  // Update Form ID
  useEffect(() => {
    const localizedBlock = getLocalizedBlock();
    if (localizedBlock?.form_id) {
      setFormId(localizedBlock.form_id);
    }
  }, [block, currentLanguage]);

  useEffect(() => {
    setIsSubmitting(isSubmittingForm);
  }, [isSubmittingForm]);

  // Update block data to translated version
  const getLocalizedBlock = () => {
    if (!block) return block;

    // If there's no translation in block, return original block
    if (!block.translations) return block;

    // Find translation in relevant language or use English translation as default
    const translation = block.translations[currentLanguage] || block.translations.en || {};

    // Merge basic properties with translations
    const localizedBlock = {
      ...block,
      title: translation.title || block.title,
      description: translation.description || block.description,
    };

    // Apply translations according to content type
    if (block.type === 'text' && block.textContent && translation.textContent) {
      localizedBlock.textContent = {
        ...block.textContent,
        content: translation.textContent.content || block.textContent.content,
      };
    } else if (block.type === 'quiz' && block.quiz && translation.quiz) {
      // Translate quiz questions
      if (block.quiz.questions && translation.quiz && translation.quiz.questions) {
        localizedBlock.quiz = {
          ...block.quiz,
          questions: block.quiz.questions.map((question, index) => {
            const translatedQuestion =
              translation.quiz.questions.length > index ? translation.quiz.questions[index] : {};
            return {
              ...question,
              question: translatedQuestion.question || question.question,
              options:
                (question.options &&
                  question.options.map((option, optIndex) => {
                    const translatedOption =
                      translatedQuestion.options && translatedQuestion.options.length > optIndex
                        ? translatedQuestion.options[optIndex]
                        : {};
                    return {
                      ...option,
                      option: translatedOption.option || option.option,
                    };
                  })) ||
                question.options,
            };
          }),
        };
      }
    } else if (block.type === 'form' && block.form_data && translation.form_data) {
      // Translate form data - here directly from translations->chapters->topics->contentBlocks
      // we should get form data instead of block.form_data

      // First, create basic structure with translated title and description from form_data
      localizedBlock.form_data = {
        ...(block.form_data || {}), // Get basic structure from original
        title: translation.form_data.title || (block.form_data ? block.form_data.title : ''),
        description:
          translation.form_data.description || (block.form_data ? block.form_data.description : ''),
      };

      // If form_data.topics exists in translation, use it
      if (translation.form_data.topics && Array.isArray(translation.form_data.topics)) {
        // Get topics and fields directly from translations
        localizedBlock.form_data.topics = translation.form_data.topics.map(
          (translatedTopic, topicIndex) => {
            // Find original form data (if exists)
            const originalTopic = block.form_data?.topics?.[topicIndex];

            // Get all translated information correctly for each topic
            const result = {
              ...translatedTopic,
              _id:
                translatedTopic._id || (originalTopic ? originalTopic._id : `topic-${topicIndex}`),
              // Process fields area in more detail - get missing fields (name, type) from original
              fields: Array.isArray(translatedTopic.fields)
                ? translatedTopic.fields.map((translatedField, fieldIndex) => {
                    // Find original field
                    const originalField = originalTopic?.fields?.[fieldIndex];

                    // Merge field values - use original for missing values
                    return {
                      ...originalField, // First get original values
                      ...translatedField, // Then add values from translation
                      // Set default values if critical fields are missing
                      name:
                        translatedField.name ||
                        (originalField ? originalField.name : `field-${fieldIndex}`),
                      type: translatedField.type || (originalField ? originalField.type : 'select'),
                      required:
                        translatedField.required ||
                        (originalField ? originalField.required : false),
                      _id:
                        translatedField._id ||
                        (originalField ? originalField._id : `field-${topicIndex}-${fieldIndex}`),
                      options: Array.isArray(translatedField.options)
                        ? translatedField.options.map((translatedOption, optionIndex) => {
                            const originalOption = originalField?.options?.[optionIndex];
                            return {
                              ...originalOption,
                              ...translatedOption,
                              value:
                                translatedOption.value ||
                                (originalOption ? originalOption.value : `${optionIndex + 1}`),
                              _id:
                                translatedOption._id ||
                                (originalOption
                                  ? originalOption._id
                                  : `option-${topicIndex}-${fieldIndex}-${optionIndex}`),
                            };
                          })
                        : originalField?.options || [],
                    };
                  })
                : originalTopic?.fields || [],
            };

            return result;
          }
        );
      }
      // If topics don't exist in translation but exist in original, use original
      else if (!localizedBlock.form_data.topics && block.form_data && block.form_data.topics) {
        localizedBlock.form_data.topics = block.form_data.topics;
      }
    }

    // Process video content translations
    if (block.videoContent && translation.videoContent) {
      // Translate video content - especially videoContent.hlsData.streamingUrls paths
      localizedBlock.videoContent = {
        ...block.videoContent,
        title: translation.videoContent.title || block.videoContent.title,
        description: translation.videoContent.description || block.videoContent.description,
      };

      // Translate HLS data
      if (translation.videoContent.hlsData) {
        localizedBlock.videoContent.hlsData = {
          ...block.videoContent.hlsData,
        };

        // Translate StreamingUrls
        if (translation.videoContent.hlsData.streamingUrls) {
          localizedBlock.videoContent.hlsData.streamingUrls =
            translation.videoContent.hlsData.streamingUrls;
        }
      }
    }

    // Process PDF content translations
    if (block.pdfContent && translation.pdfContent) {
      localizedBlock.pdfContent = {
        ...block.pdfContent,
        url: translation.pdfContent.url || block.pdfContent.url,
        title: translation.pdfContent.title || block.pdfContent.title,
        description: translation.pdfContent.description || block.pdfContent.description,
      };
    }

    // Translate usecase type
    if (
      (block.type === 'usecase' || block.usecase_slug || block.usecase_type) &&
      translation.usecase_slug
    ) {
      localizedBlock.usecase_slug = translation.usecase_slug;
    }

    // Process playground content translations
    if (block.playground_type && translation.playground_type) {
      localizedBlock.playground_type = translation.playground_type;

      if (translation.initialPrompt) {
        localizedBlock.initialPrompt = translation.initialPrompt;
      }
    }

    return localizedBlock;
  };

  // Form values change handler
  const handleFormChange = (fieldName, value) => {
    setFormValues((prev) => ({
      ...prev,
      [fieldName]: value,
    }));
  };

  // File upload function
  const handleFileUpload = async (files, fieldName) => {
    if (!files || files.length === 0) return;

    setUploadingFiles((prev) => ({ ...prev, [fieldName]: true }));
    setUploadErrors((prev) => ({ ...prev, [fieldName]: null }));

    try {
      const uploadedFiles = [];

      for (const file of files) {
        // File size check (50MB)
        if (file.size > 50 * 1024 * 1024) {
          throw new Error(`File ${file.name} is too large. Maximum size is 50MB.`);
        }

        const formData = new FormData();
        formData.append('file', file);

        const result = await uploadFile(formData, token);
        uploadedFiles.push(result);
      }

      // Update form state
      const newValue = files.length === 1 ? uploadedFiles[0] : uploadedFiles;
      handleFormChange(fieldName, newValue);
    } catch (error) {
      console.error('File upload error:', error);
      setUploadErrors((prev) => ({ ...prev, [fieldName]: error.message }));
    } finally {
      setUploadingFiles((prev) => ({ ...prev, [fieldName]: false }));
    }
  };

  // File delete function
  const handleFileRemove = (fieldName, fileIndex = null) => {
    const currentValue = formValues[fieldName];

    if (Array.isArray(currentValue)) {
      if (fileIndex !== null) {
        const newFiles = currentValue.filter((_, index) => index !== fileIndex);
        handleFormChange(fieldName, newFiles.length > 0 ? newFiles : null);
      } else {
        handleFormChange(fieldName, null);
      }
    } else {
      handleFormChange(fieldName, null);
    }
  };

  // Form submit handler
  const handleFormSubmit = async (e) => {
    e.preventDefault();
    setFormSubmitting(true);
    setFormErrors({});

    try {
      // Check empty answers
      const errors = {};

      // Collect all form fields
      const allFields = [];
      formData?.topics?.forEach((topic) => {
        if (topic.fields && Array.isArray(topic.fields)) {
          allFields.push(...topic.fields);
        }
      });

      // Check all fields, not just required ones
      allFields.forEach((field) => {
        const value = formValues[field.name];
        const isEmpty =
          value === undefined ||
          value === null ||
          value === '' ||
          (Array.isArray(value) && value.length === 0) ||
          (typeof value === 'string' && value.trim() === '');

        // Always show error for required fields
        if (field.required && isEmpty) {
          errors[field.name] = t('common.fieldRequired');
        }
        // Check for non-required but non-empty fields
        else if (isEmpty && !field.allowEmpty) {
          errors[field.name] = t('common.fieldCannotBeEmpty');
        }
      });

      // Don't submit form if there are errors
      if (Object.keys(errors).length > 0) {
        setFormErrors(errors);
        toast.error(t('common.pleaseCompleteRequiredFields'), {
          position: 'bottom-right',
          autoClose: 3000,
        });
        setFormSubmitting(false);
        return;
      }

      // Form type conversion function
      const getFormType = (type) => {
        switch (type?.toLowerCase()) {
          case 'ideation':
            return 'ideation';
          case 'feedback':
            return 'feedback';
          case 'help':
            return 'help';
          case 'assessment':
            return 'assessment';
          default:
            return 'feedback';
        }
      };

      // Response type conversion function
      const getResponseType = (type) => {
        switch (type?.toLowerCase()) {
          case 'text':
            return 'text';
          case 'number':
            return 'number';
          case 'email':
            return 'email';
          case 'textarea':
            return 'textarea';
          case 'select':
            return 'select';
          case 'radio':
            return 'radio';
          case 'checkbox':
            return 'checkbox';
          case 'date':
            return 'date';
          case 'file':
            return 'file';
          case 'feedback':
            return 'radio';
          default:
            return 'text';
        }
      };

      // Form responses preparation function
      const prepareFormResponses = () => {
        return (formData?.topics || []).flatMap((topic) =>
          (topic.fields || []).map((field) => {
            const value = formValues[field.name];
            // Don't allow empty values, send at least empty string
            const safeValue = value === undefined || value === null ? '' : value;

            return {
              fieldId: field._id,
              name: field.name,
              type: getResponseType(field.type),
              value: safeValue,
            };
          })
        );
      };

      // Prepare all form responses
      const responses = prepareFormResponses();

      // Prepare source details
      const sourceDetails = {
        courseId: courseId || null,
        chapterIndex: chapterIndex !== undefined ? chapterIndex : null,
        topicIndex: topicIndex !== undefined ? topicIndex : null,
        blockId: block._id || null,
      };

      await submitFormResponse({
        formId: block._id,
        formType: getFormType(formData?.type),
        title: formData?.title || 'Form Response',
        description: formData?.description || 'Form submission from course content',
        responses,
        source: 'course-content',
        sourceDetails,
      }).then((response) => {
        setFormSubmitted(true);

        // Call onFormSubmit function after form is successfully submitted
        if (onFormSubmit) {
          onFormSubmit(block._id);
        }
      });
    } catch (error) {
      console.error('Form submission error:', error);
    } finally {
      setFormSubmitting(false);
    }
  };

  // Form field rendering function
  const renderField = (field) => {
    if (!field || !field.type) {
      console.error('Invalid field data:', field);
      return null;
    }

    const fieldId = `field-${field._id || 'unknown'}`;
    const isRequired = field.required || false;
    const fieldName = field.name || `field-${field._id || 'unknown'}`;
    const fieldError = formErrors[fieldName];

    // Missing data check
    if (!fieldName) {
      console.error('Name value not found for field:', field);
      return null;
    }

    // Use field.name value as unique identifier
    const fieldValue = fieldName in formValues ? formValues[fieldName] : '';

    // Options check
    const hasValidOptions =
      field.options && Array.isArray(field.options) && field.options.length > 0;

    switch (field.type) {
      case 'text':
      case 'email':
      case 'number':
        return (
          <TextField
            key={fieldId}
            id={fieldId}
            label={field.label}
            type={field.type}
            value={fieldValue}
            onChange={(e) => handleFormChange(fieldName, e.target.value)}
            fullWidth
            margin="normal"
            required={isRequired}
            disabled={formSubmitted}
            error={!!fieldError}
            helperText={fieldError}
          />
        );

      case 'textarea':
        return (
          <TextField
            key={fieldId}
            id={fieldId}
            label={field.label}
            multiline
            rows={4}
            value={fieldValue}
            onChange={(e) => handleFormChange(fieldName, e.target.value)}
            fullWidth
            margin="normal"
            required={isRequired}
            disabled={formSubmitted}
            error={!!fieldError}
            helperText={fieldError}
          />
        );

      case 'select':
        if (!hasValidOptions) {
          console.error('No valid options found for select field:', field);
          return (
            <Typography color="error" variant="body2" gutterBottom>
              {t('courseContent.noOptionsFound')}
            </Typography>
          );
        }

        return (
          <FormControl
            key={fieldId}
            fullWidth
            margin="normal"
            required={isRequired}
            disabled={formSubmitted}
            error={!!fieldError}
          >
            <InputLabel id={`${fieldId}-label`}>{field.label}</InputLabel>
            <Select
              labelId={`${fieldId}-label`}
              id={fieldId}
              value={fieldValue}
              label={field.label}
              onChange={(e) => handleFormChange(fieldName, e.target.value)}
            >
              {field.options &&
                field.options.map((option) => (
                  <MenuItem
                    key={option._id || option.value || `option-${option.label}`}
                    value={option.value || `value-${option.label}`}
                  >
                    {option.label}
                  </MenuItem>
                ))}
            </Select>
            {fieldError && <FormHelperText error>{fieldError}</FormHelperText>}
          </FormControl>
        );

      case 'radio':
        return (
          <FormControl
            key={fieldId}
            component="fieldset"
            margin="normal"
            required={isRequired}
            disabled={formSubmitted}
            error={!!fieldError}
          >
            <Typography className="content-block__form-feedback-title" variant="subtitle1">
              {field.label}
            </Typography>
            <RadioGroup
              value={fieldValue}
              onChange={(e) => handleFormChange(fieldName, e.target.value)}
            >
              {field.options &&
                field.options.map((option) => (
                  <FormControlLabel
                    key={option._id || option.value}
                    value={option.value}
                    control={<Radio />}
                    label={option.label}
                    className="content-block__form-control-label"
                  />
                ))}
            </RadioGroup>
            {fieldError && <FormHelperText error>{fieldError}</FormHelperText>}
          </FormControl>
        );

      case 'checkbox':
        // We keep values as array for checkbox
        const checkboxValues = Array.isArray(fieldValue) ? fieldValue : [];

        return (
          <FormControl
            key={fieldId}
            component="fieldset"
            margin="normal"
            required={isRequired}
            disabled={formSubmitted}
          >
            <Typography className="content-block__form-feedback-title" variant="subtitle1">
              {field.label}
            </Typography>
            <FormGroup>
              {field.options &&
                field.options.map((option) => (
                  <FormControlLabel
                    key={option._id || option.value}
                    control={
                      <Checkbox
                        checked={checkboxValues.includes(option.value)}
                        onChange={(e) => {
                          const newValues = e.target.checked
                            ? [...checkboxValues, option.value]
                            : checkboxValues.filter((v) => v !== option.value);
                          handleFormChange(fieldName, newValues);
                        }}
                      />
                    }
                    label={option.label}
                    className="content-block__form-control-label"
                  />
                ))}
            </FormGroup>
            {/* {isRequired && <FormHelperText>This field is required</FormHelperText>} */}
          </FormControl>
        );

      case 'file':
        const currentFiles = fieldValue;
        const isUploading = uploadingFiles[fieldName];
        const uploadError = uploadErrors[fieldName];

        return (
          <FormControl
            key={fieldId}
            fullWidth
            margin="normal"
            required={isRequired}
            disabled={formSubmitted}
            error={!!fieldError}
          >
            <Typography variant="subtitle1" sx={{ mb: 1 }}>
              {field.label}
            </Typography>
            <Box sx={{ mt: 1 }}>
              <input
                type="file"
                multiple={field.file_settings?.multiple || false}
                accept={
                  field.file_settings?.allowed_types?.join(',') ||
                  'image/*,application/pdf,.doc,.docx,.txt'
                }
                onChange={(e) => {
                  const files = Array.from(e.target.files);
                  if (files.length > 0) {
                    handleFileUpload(files, fieldName);
                  }
                }}
                disabled={formSubmitted || isUploading}
                style={{ display: 'none' }}
                id={`file-input-${fieldId}`}
              />
              <label htmlFor={`file-input-${fieldId}`}>
                <Button
                  variant="outlined"
                  component="span"
                  startIcon={isUploading ? <CircularProgress size={20} /> : <AttachFile />}
                  disabled={formSubmitted || isUploading}
                  fullWidth
                  sx={{ mb: 1 }}
                >
                  {isUploading ? t('common.uploading') : t('common.chooseFiles')}
                </Button>
              </label>

              {uploadError && (
                <Typography color="error" variant="body2" sx={{ mb: 1 }}>
                  {uploadError}
                </Typography>
              )}

              {currentFiles && (
                <Box sx={{ mt: 1 }}>
                  {Array.isArray(currentFiles) ? (
                    currentFiles.map((file, index) => (
                      <Box
                        key={index}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          p: 1,
                          border: '1px solid #e0e0e0',
                          borderRadius: 1,
                          mb: 1,
                        }}
                      >
                        <Typography variant="body2">{file.originalName || file.name}</Typography>
                        <IconButton
                          size="small"
                          onClick={() => handleFileRemove(fieldName, index)}
                          disabled={formSubmitted}
                        >
                          <Delete />
                        </IconButton>
                      </Box>
                    ))
                  ) : (
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        p: 1,
                        border: '1px solid #e0e0e0',
                        borderRadius: 1,
                        mb: 1,
                      }}
                    >
                      <Typography variant="body2">
                        {currentFiles.originalName || currentFiles.name}
                      </Typography>
                      <IconButton
                        size="small"
                        onClick={() => handleFileRemove(fieldName)}
                        disabled={formSubmitted}
                      >
                        <Delete />
                      </IconButton>
                    </Box>
                  )}
                </Box>
              )}

              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
                Max size: {field.file_settings?.max_size || 50}MB | Allowed types:{' '}
                {field.file_settings?.allowed_types?.join(', ') ||
                  'image/*, application/pdf, .doc, .docx, .txt'}
                {field.file_settings?.multiple && ' | Multiple files allowed'}
              </Typography>
            </Box>
            {fieldError && <FormHelperText error>{fieldError}</FormHelperText>}
          </FormControl>
        );

      case 'feedback':
        const feedbackValue = fieldValue !== '' ? Number(fieldValue) : '';

        return (
          <FormControl
            key={fieldId}
            component="fieldset"
            margin="normal"
            required={isRequired}
            disabled={formSubmitted}
            className="content-block__form-feedback"
          >
            <Typography className="content-block__form-feedback-title" variant="subtitle1">
              {field.label}
            </Typography>
            <Box sx={{ mt: 2, mb: 1 }}>
              <Grid container alignItems="center">
                {[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((value) => (
                  <Grid item key={value}>
                    <Button
                      variant={feedbackValue === value ? 'contained' : 'outlined'}
                      color={feedbackValue === value ? 'primary' : 'inherit'}
                      onClick={() => handleFormChange(fieldName, value)}
                      sx={{
                        borderRadius: '4px',
                      }}
                    >
                      {value}
                    </Button>
                  </Grid>
                ))}
              </Grid>
            </Box>
            {/* {isRequired && <FormHelperText>This field is required</FormHelperText>} */}
          </FormControl>
        );

      default:
        return null;
    }
  };

  // Fetch usecase data
  useEffect(() => {
    const fetchUseCase = async () => {
      // First check block.type === 'usecase'
      if (block.type === 'usecase' || block.usecase_slug || block.usecase_type) {
        const usecaseSlug =
          block.usecase_slug ||
          (block.type === 'usecase' && block.usecase_slug ? block.usecase_slug : null) ||
          (block.usecase_type ? block.usecase_type : null);

        if (usecaseSlug) {
          setUseCaseLoading(true);
          setUseCaseError(null);
          try {
            // Update API call - use correct URL
            const response = await axios.get(`${CDS_API_URL}/usecase/slug/${usecaseSlug}`, {
              headers: {
                'Content-Type': 'application/json',
                'x-api-key': CDS_API_KEY,
                // Add if authentication header is required
                // 'Authorization': `Bearer ${token}`
              },
            });

            if (response.data && response.data.data) {
              setUseCase(response.data.data);
            } else if (response.data && response.data.usecase) {
              // Alternative data structure check
              setUseCase(response.data.usecase);
            } else if (response.data && typeof response.data === 'object' && !response.data.error) {
              // Use response.data directly
              setUseCase(response.data);
            } else {
              setUseCaseError('Invalid usecase data format');
            }
          } catch (error) {
            setUseCaseError(
              error.response?.data?.message || error.message || 'Failed to load usecase'
            );
          } finally {
            setUseCaseLoading(false);
          }
        } else {
          setUseCaseError('Missing usecase_slug for usecase block');
        }
      }
    };

    fetchUseCase();
  }, [block.usecase_slug, block.type, block.usecase_type]);

  // Helper function to extract YouTube video ID
  const getYouTubeVideoId = (url) => {
    if (!url) return null;
    const patterns = [
      /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/i,
      /^[a-zA-Z0-9_-]{11}$/,
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) return match[1];
    }

    return null;
  };

  // Helper function to extract video URL - updated for translations
  const getVideoUrl = (block) => {
    if (!block.videoContent) {
      return null;
    }

    // Use translated version of block
    const localizedBlock = getLocalizedBlock();

    // For YouTube video
    if (localizedBlock.videoContent.type === 'youtube') {
      return localizedBlock.videoContent.youtubeUrl;
    }

    // For HLS video
    if (localizedBlock.videoContent.type === 'hls') {
      if (
        !localizedBlock.videoContent.hlsData ||
        !localizedBlock.videoContent.hlsData.streamingUrls
      ) {
        return null;
      }

      const streamingUrls = localizedBlock.videoContent.hlsData.streamingUrls;

      // Case 1: if streamingUrls is an array
      if (Array.isArray(streamingUrls) && streamingUrls.length > 0) {
        const firstUrl = streamingUrls[0];

        // Case 1.1: if first element is an object and has paths property
        if (typeof firstUrl === 'object' && firstUrl !== null && firstUrl.paths) {
          if (Array.isArray(firstUrl.paths) && firstUrl.paths.length > 0) {
            return firstUrl.paths[0];
          } else if (typeof firstUrl.paths === 'string') {
            return firstUrl.paths;
          }
        }
        // Case 1.2: if first element is directly a string
        else if (typeof firstUrl === 'string') {
          return firstUrl;
        }
      }
      // Case 2: if streamingUrls is an object
      else if (typeof streamingUrls === 'object' && streamingUrls !== null) {
        // Case 2.1: if paths property is an array
        if (Array.isArray(streamingUrls.paths) && streamingUrls.paths.length > 0) {
          return streamingUrls.paths[0];
        }
        // Case 2.2: if paths property is a string
        else if (typeof streamingUrls.paths === 'string') {
          return streamingUrls.paths;
        }
        // Case 2.3: if _id property exists (special case)
        else if (
          streamingUrls._id &&
          Array.isArray(streamingUrls.paths) &&
          streamingUrls.paths.length > 0
        ) {
          return streamingUrls.paths[0];
        }
      }
      // Case 3: if streamingUrls is a string
      else if (typeof streamingUrls === 'string') {
        return streamingUrls;
      }
    }

    // Old data structure check
    if (localizedBlock.videoContent.url) {
      return localizedBlock.videoContent.url;
    }

    return null;
  };

  // Helper function to extract subtitle data from course structure
  const getSubtitleData = (block) => {
    if (!block.videoContent) {
      return null;
    }

    // Use translated version of block
    const localizedBlock = getLocalizedBlock();

    // For HLS video with subtitle data
    if (localizedBlock.videoContent.type === 'hls' && localizedBlock.videoContent.hlsData) {
      const streamingUrls = localizedBlock.videoContent.hlsData.streamingUrls;

      // Case 1: if streamingUrls is an array
      if (Array.isArray(streamingUrls) && streamingUrls.length > 0) {
        const firstUrl = streamingUrls[0];

        // Case 1.1: if first element is an object and has subtitles property
        if (typeof firstUrl === 'object' && firstUrl !== null && firstUrl.subtitles) {
          // Convert to the format expected by EnhancedVideoPlayer
          return {
            translations: firstUrl.subtitles,
          };
        }
      }
      // Case 2: if streamingUrls is an object
      else if (typeof streamingUrls === 'object' && streamingUrls !== null) {
        if (streamingUrls.subtitles) {
          // Convert to the format expected by EnhancedVideoPlayer
          return {
            translations: streamingUrls.subtitles,
          };
        }
      }
    }

    return null;
  };

  const handleComplete = async () => {
    // If processing is already being done, skip completion process
    if (isProcessing.current) {
      return;
    }

    // Determine playground type
    const isGptPlayground =
      block.playground_type === 'gpt' ||
      block.displayType === 'playground-gpt' ||
      block.type === 'playground-gpt';

    // If already completed and not playground type, skip process
    // Note: Always continue processing for ChatGPT Playground
    if (!isGptPlayground && (isContentCompleted || isTopicCompleted)) {
      return;
    }

    try {
      isProcessing.current = true;

      // Mark content as completed
      setIsContentCompleted(true);

      // Send notification to parent component only once
      // NOTE: We're sending block ID to onContentCompleted to specify interactive content type
      if (onContentCompleted) {
        if (isGptPlayground) {
          // Send block ID to onContentCompleted to indicate playground completion
          onContentCompleted('interactive', block._id);
        } else {
          onContentCompleted();
        }
      }

      // If this is a usecase, onUsecaseGenerate should also be called
      if (
        onUsecaseGenerate &&
        (block.type === 'usecase' || block.usecase_slug || block.usecase_type)
      ) {
        onUsecaseGenerate(block._id);
      }
    } catch (error) {
      console.error('❌ Error in handleComplete:', error);
      toast.error(t('courseContent.errorCompletingContent', 'Error completing content!'), {
        position: 'bottom-right',
        autoClose: 3000,
      });
    } finally {
      // Indicate that the process is finished - with a slight delay
      setTimeout(() => {
        isProcessing.current = false;
      }, 500);
    }
  };

  // Add postMessage listener for Heygen Video Creator
  useEffect(() => {
    const handleMessage = (event) => {
      // Check messages from HeygenVideoCreator
      if (event.data && event.data.type === 'HEYGEN_VIDEO_COMPLETED') {
        // Call HandleComplete
        handleComplete();
      }
    };

    // Add event listener
    window.addEventListener('message', handleMessage);

    // Cleanup
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []); // handleComplete function is created within component so no need to add as dependency, eslint-disable-line react-hooks/exhaustive-deps

  // PDF URL processing function
  const processPdfUrl = (url) => {
    if (url && url.includes('aibsassets.blob.core.windows.net')) {
      return url.split('?')[0];
    }

    return url;
  };

  // Decode HTML content and fix PDF content
  const decodeAndFixPdfContent = (content) => {
    if (!content) return '';

    // Decode HTML characters
    const decodedContent = content
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#039;/g, "'");

    // Fix PDF URLs
    const fixedContent = decodedContent.replace(
      /(href=["'])(.*?\.pdf)(["'])/gi,
      (match, p1, p2, p3) => {
        const fixedUrl = processPdfUrl(p2);
        return `${p1}${fixedUrl}${p3}`;
      }
    );

    return fixedContent;
  };

  // Load PDF as base64
  const loadPdfAsBase64 = async (url) => {
    try {
      setIsLoading(true);
      const response = await fetch(url, {
        method: 'GET',
        mode: 'cors',
        headers: {
          Accept: 'application/pdf',
          Origin: window.location.origin,
        },
        credentials: 'include',
      });

      if (!response.ok) {
        // Try downloading PDF through proxy
        const proxyUrl = `${CDS_API_URL}/proxy/pdf?url=${encodeURIComponent(url)}`;
        const proxyResponse = await fetch(proxyUrl, {
          headers: {
            'x-api-key': CDS_API_KEY,
          },
        });

        if (!proxyResponse.ok) {
          throw new Error('PDF not loaded');
        }

        const blob = await proxyResponse.blob();
        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onloadend = () => {
            const base64data = reader.result;
            setPdfBase64(base64data);
            setIsLoading(false);
            resolve(base64data);
          };
          reader.onerror = reject;
          reader.readAsDataURL(blob);
        });
      }

      const blob = await response.blob();
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          const base64data = reader.result;
          setPdfBase64(base64data);
          setIsLoading(false);
          resolve(base64data);
        };
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      console.error('Error loading PDF:', error);
      setPdfError(error);
      setIsLoading(false);
      throw error;
    }
  };

  // Re-render when PDF page changes
  useEffect(() => {
    if (
      block.type === 'pdf' ||
      (block.textContent?.content && block.textContent.content.includes('.pdf'))
    ) {
      const hrefMatch = block.textContent?.content?.match(/href=["']([^"']+)["']/);
      if (hrefMatch && hrefMatch[1]) {
        const pdfUrl = processPdfUrl(hrefMatch[1]);
        loadPdfAsBase64(pdfUrl);
      }
    }
  }, [block]);

  // Helper function to extract video URL from text content
  const extractVideoUrlFromTextContent = (content) => {
    if (!content) return null;

    // Detect source src inside video tag
    const videoMatch = content.match(
      /<video[^>]*>[\s\S]*?<source[^>]+src=["']([^"']+)["'][^>]*>[\s\S]*?<\/video>/i
    );
    if (videoMatch && videoMatch[1]) {
      return videoMatch[1];
    }

    // Detect iframe src (optional)
    const iframeMatch = content.match(/<iframe[^>]+src=["']([^"']+)["'][^>]*>/i);
    if (iframeMatch && iframeMatch[1]) {
      return iframeMatch[1];
    }

    return null;
  };

  // Check if video URL is playable
  const isPlayableVideoUrl = (url) => {
    if (!url) return false;

    // Microsoft Learn video embed check
    if (url.includes('learn.microsoft.com') && url.includes('video-embed')) {
      return true;
    }

    // Common video formats
    const supportedVideoExtensions = ['.mp4', '.webm', '.ogg', '.mov', '.avi', '.mkv', '.m3u8'];
    const hasVideoExtension = supportedVideoExtensions.some((ext) =>
      url.toLowerCase().includes(ext)
    );

    // YouTube, Vimeo and other platforms
    const supportedVideoSites = ['youtube.com', 'youtu.be', 'vimeo.com', 'dailymotion.com'];
    const isFromVideoSite = supportedVideoSites.some((site) => url.toLowerCase().includes(site));

    // HLS stream
    const isHLS = url.toLowerCase().includes('.m3u8');

    return hasVideoExtension || isFromVideoSite || isHLS;
  };

  // Check if video URL should be rendered as iframe
  const shouldRenderAsIframe = (url) => {
    if (!url) return false;

    // Microsoft Learn video embed URLs should be rendered as iframe
    if (url.includes('learn.microsoft.com') && url.includes('video-embed')) {
      return true;
    }

    // Other embed URLs
    const embedSites = ['embed', 'player', 'iframe'];
    return embedSites.some((keyword) => url.toLowerCase().includes(keyword));
  };

  const renderContent = () => {
    // Use getLocalizedBlock function to get block content with translations
    const localizedBlock = getLocalizedBlock();

    // Check usecase_slug or usecase_type field for usecase content
    if (
      localizedBlock.type === 'usecase' ||
      localizedBlock.usecase_slug ||
      localizedBlock.usecase_type
    ) {
      // Determine usecase slug
      const usecaseSlug =
        localizedBlock.usecase_slug ||
        (localizedBlock.type === 'usecase' && localizedBlock.usecase_slug
          ? localizedBlock.usecase_slug
          : null) ||
        (localizedBlock.usecase_type ? localizedBlock.usecase_type : null);

      if (!usecaseSlug) {
        return (
          <Box sx={{ p: 2, textAlign: 'center', color: 'error.main' }}>
            <Typography variant="body1">{t('courseContent.usecaseSlugNotFound')}</Typography>
            <Typography variant="body2">
              Usecase content: {JSON.stringify(localizedBlock, null, 2)}
            </Typography>
          </Box>
        );
      }

      return (
        <Box sx={{ p: 2 }}>
          <>
            {useCaseLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : useCaseError ? (
              <Box sx={{ color: 'error.main', p: 2 }}>
                <Typography color="error" gutterBottom>
                  {t('courseContent.errorLoadingUsecase')}
                </Typography>
                <Typography variant="body2">{useCaseError}</Typography>
                <Typography variant="body2" sx={{ mt: 1 }}>
                  Usecase slug: {usecaseSlug}
                </Typography>
                <Button
                  variant="outlined"
                  color="primary"
                  size="small"
                  sx={{ mt: 2 }}
                  onClick={() => {
                    // Call API again
                    const fetchUseCase = async () => {
                      setUseCaseLoading(true);
                      setUseCaseError(null);
                      try {
                        const response = await axios.get(
                          `${CDS_API_URL}/usecase/slug/${usecaseSlug}`,
                          {
                            headers: {
                              'Content-Type': 'application/json',
                              'x-api-key': CDS_API_KEY,
                            },
                          }
                        );
                        if (response.data && response.data.data) {
                          setUseCase(response.data.data);
                        } else if (response.data && response.data.usecase) {
                          setUseCase(response.data.usecase);
                        } else if (
                          response.data &&
                          typeof response.data === 'object' &&
                          !response.data.error
                        ) {
                          setUseCase(response.data);
                        } else {
                          setUseCaseError('Invalid usecase data format');
                        }
                      } catch (error) {
                        setUseCaseError(
                          error.response?.data?.message || error.message || 'Failed to load usecase'
                        );
                      } finally {
                        setUseCaseLoading(false);
                      }
                    };
                    fetchUseCase();
                  }}
                >
                  {t('common.tryAgain')}
                </Button>
              </Box>
            ) : useCase ? (
              <>
                {localizedBlock.usecase_type === 'image' || localizedBlock?.type === 'image' ? (
                  <ImageUsecase
                    data={{
                      ...localizedBlock,
                      api_type: 'dalle',
                      'dall-e_settings':
                        useCase?.translations?.[convertedLanguage]?.['dall-e_settings'] ||
                        useCase?.['dall-e_settings'],
                      translations: useCase?.translations || {},
                    }}
                    id={localizedBlock?._id}
                    disableBack={true}
                    onlyShowUsecase={false}
                    initialPrompt={
                      useCase?.translations?.[convertedLanguage]?.['dall-e_settings']
                        ?.form_fields?.[0]?.default_value ||
                      (useCase?.translations?.[convertedLanguage]?.['dall-e_settings']
                        ?.form_fields &&
                      useCase?.translations?.[convertedLanguage]?.['dall-e_settings']?.form_fields
                        .length > 0
                        ? useCase?.translations?.[convertedLanguage]?.['dall-e_settings']
                            ?.form_fields[0]?.default_value
                        : '') ||
                      ''
                    }
                    onGenerate={() => {
                      handleComplete();
                    }}
                  />
                ) : (
                  <SingleApp
                    slug={usecaseSlug}
                    onGenerate={(completed = true) => {
                      handleComplete();
                    }}
                    disableAutoComplete={true}
                    isCompleted={isTopicCompleted || isContentCompleted}
                    useCase={localizedBlock}
                    onlyShowUsecase={false}
                    isLoading={useCaseLoading}
                    skipInitialLoad={false}
                  />
                )}
              </>
            ) : (
              <Box sx={{ textAlign: 'center', p: 2 }}>
                <Typography color="text.secondary">
                  {t('courseContent.noUsecaseDataFor')} {usecaseSlug}
                </Typography>
                <Typography variant="body2">Usecase slug: {usecaseSlug}</Typography>
                <Button
                  variant="outlined"
                  color="primary"
                  size="small"
                  sx={{ mt: 2 }}
                  onClick={() => {
                    // Call API again
                    const fetchUseCase = async () => {
                      setUseCaseLoading(true);
                      setUseCaseError(null);
                      try {
                        const response = await axios.get(`${CDS_API_URL}/usecase/${usecaseSlug}`, {
                          headers: {
                            'Content-Type': 'application/json',
                            'x-api-key': CDS_API_KEY,
                          },
                        });
                        if (response.data && response.data.data) {
                          setUseCase(response.data.data);
                        } else if (response.data && response.data.usecase) {
                          setUseCase(response.data.usecase);
                        } else if (
                          response.data &&
                          typeof response.data === 'object' &&
                          !response.data.error
                        ) {
                          setUseCase(response.data);
                        } else {
                          setUseCaseError('Invalid usecase data format');
                        }
                      } catch (error) {
                        setUseCaseError(
                          error.response?.data?.message || error.message || 'Failed to load usecase'
                        );
                      } finally {
                        setUseCaseLoading(false);
                      }
                    };
                    fetchUseCase();
                  }}
                >
                  {t('common.tryAgain')}
                </Button>
              </Box>
            )}
          </>
        </Box>
      );
    }

    // Handle playground_type for content blocks that should display as playground
    if (localizedBlock.playground_type) {
      return (
        <Box sx={{ p: 2 }}>
          {localizedBlock.description && (
            <Typography variant="body1" color="text.secondary" gutterBottom>
              {localizedBlock.description}
            </Typography>
          )}

          <Box>
            {localizedBlock.playground_type === 'gpt' ? (
              <CopilotChatPlayground
                initialPrompt={localizedBlock.initialPrompt || ''}
                onGenerate={() => {
                  // Direct call to save progress - calling unconditionally
                  handleComplete();
                }}
                disableBack={true}
                temperature={localizedBlock.temperature || 0.7}
                topP={localizedBlock.topP || 1}
                frequencyPenalty={localizedBlock.frequencyPenalty || 0}
                presencePenalty={localizedBlock.presencePenalty || 0}
                showNotification={true}
                defaultSettingsOpen={true}
                notificationMessage={t('courseContent.playground.completed')}
              />
            ) : localizedBlock.playground_type === 'dalle' ? (
              <DallEPlayground
                initialPrompt={localizedBlock.initialPrompt || ''}
                onGenerate={isTopicCompleted || isContentCompleted ? undefined : handleComplete}
                disableBack={true}
              />
            ) : localizedBlock.playground_type === 'heygen' ? (
              <HeygenVideoCreator
                id={localizedBlock.heygen_id || ''}
                onGenerate={() => handleComplete()}
              />
            ) : (
              <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
                <Typography variant="body1">{t('courseContent.playgroundNotSupported')}</Typography>
              </Box>
            )}
          </Box>
        </Box>
      );
    }

    // Handle displayType for playground content blocks
    if (
      localizedBlock.displayType === 'playground-gpt' ||
      localizedBlock.displayType === 'playground-dalle' ||
      localizedBlock.displayType === 'playground-heygen'
    ) {
      return (
        <Box sx={{ p: 2 }}>
          <Box>
            {localizedBlock.displayType === 'playground-gpt' ? (
              <CopilotChatPlayground
                initialPrompt={localizedBlock.initialPrompt || ''}
                onGenerate={() => {
                  // Direct call to save progress - calling unconditionally

                  handleComplete();
                }}
                disableBack={true}
                temperature={localizedBlock.temperature || 0.7}
                topP={localizedBlock.topP || 1}
                frequencyPenalty={localizedBlock.frequencyPenalty || 0}
                presencePenalty={localizedBlock.presencePenalty || 0}
                showNotification={true}
                notificationMessage={t('courseContent.playground.completed')}
              />
            ) : localizedBlock.displayType === 'playground-dalle' ? (
              <DallEPlayground
                initialPrompt={localizedBlock.initialPrompt || ''}
                onGenerate={isTopicCompleted || isContentCompleted ? undefined : handleComplete}
                disableBack={true}
              />
            ) : localizedBlock.displayType === 'playground-heygen' ? (
              <HeygenVideoCreator
                id={localizedBlock.heygen_id || ''}
                onGenerate={() => handleComplete()}
              />
            ) : (
              <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
                <Typography variant="body1">{t('courseContent.playgroundNotSupported')}</Typography>
              </Box>
            )}
          </Box>
        </Box>
      );
    }

    // Handle type for playground content blocks
    if (
      localizedBlock.type === 'playground-gpt' ||
      localizedBlock.type === 'playground-dalle' ||
      localizedBlock.type === 'playground-heygen'
    ) {
      return (
        <Box sx={{ p: 2 }}>
          <Box>
            {localizedBlock.type === 'playground-gpt' ? (
              <CopilotChatPlayground
                initialPrompt={localizedBlock.initialPrompt || ''}
                onGenerate={() => {
                  // Direct call to save progress - calling unconditionally

                  handleComplete();
                }}
                disableBack={true}
                temperature={localizedBlock.temperature || 0.7}
                topP={localizedBlock.topP || 1}
                frequencyPenalty={localizedBlock.frequencyPenalty || 0}
                presencePenalty={localizedBlock.presencePenalty || 0}
                showNotification={true}
                notificationMessage={t('courseContent.playground.completed')}
              />
            ) : localizedBlock.type === 'playground-dalle' ? (
              <DallEPlayground
                initialPrompt={localizedBlock.initialPrompt || ''}
                onGenerate={isTopicCompleted || isContentCompleted ? undefined : handleComplete}
                disableBack={true}
              />
            ) : localizedBlock.type === 'playground-heygen' ? (
              <HeygenVideoCreator
                id={localizedBlock.heygen_id || ''}
                onGenerate={() => handleComplete()}
              />
            ) : (
              <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
                <Typography variant="body1">{t('courseContent.playgroundNotSupported')}</Typography>
              </Box>
            )}
          </Box>
        </Box>
      );
    }

    switch (localizedBlock.type) {
      case 'text':
        // Extract video URL from text content
        const embeddedVideoUrl = extractVideoUrlFromTextContent(
          localizedBlock.textContent?.content
        );
        const hasPlayableVideo = isPlayableVideoUrl(embeddedVideoUrl);

        return (
          <Box sx={{ p: 2 }}>
            {/* If there's a playable video, show video player first */}
            {hasPlayableVideo && (
              <Box sx={{ mb: 3 }} className="text-content__embedded-video">
                <Box
                  className={`topic-content__video-container ${
                    shouldRenderAsIframe(embeddedVideoUrl)
                      ? 'iframe-video-container'
                      : 'enhanced-video-container'
                  }`}
                >
                  {shouldRenderAsIframe(embeddedVideoUrl) ? (
                    // iframe for Microsoft Learn and other embed URLs
                    <Box
                      sx={{
                        position: 'relative',
                        paddingBottom: '56.25%', // 16:9 aspect ratio
                        height: 0,
                        overflow: 'hidden',
                        borderRadius: 1,
                        '& iframe': {
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          width: '100%',
                          height: '100%',
                          border: 'none',
                        },
                      }}
                    >
                      <iframe
                        src={embeddedVideoUrl}
                        allowFullScreen
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                        title="Embedded Video"
                      />
                    </Box>
                  ) : (
                    // EnhancedVideoPlayer for normal video files
                    <EnhancedVideoPlayer
                      videoUrl={embeddedVideoUrl}
                      onComplete={handleVideoEnd}
                      courseId={courseId}
                      chapterIndex={chapterIndex}
                      topicIndex={topicIndex}
                      onTimeUpdate={onTimeUpdate}
                      subtitles={{}}
                      autoPlay={false}
                    />
                  )}
                </Box>
                <Divider sx={{ mt: 2, mb: 2 }} />
              </Box>
            )}

            {/* Original text content */}
            {localizedBlock.textContent?.content ? (
              <Box
                sx={{
                  '& img': { maxWidth: '100%', height: 'auto' },
                  '& p': { margin: '1em 0' },
                  '& .pdf-container': {
                    position: 'relative',
                    height: '80vh',
                    width: '100%',
                    overflow: 'hidden',
                    '& object, & iframe': {
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: '100%',
                      border: 'none',
                    },
                  },
                  // Hide video tags since we're showing them as player above
                  '& video': hasPlayableVideo ? { display: 'none !important' } : {},
                }}
                dangerouslySetInnerHTML={{
                  __html: decodeAndFixPdfContent(localizedBlock.textContent.content),
                }}
              />
            ) : (
              <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
                <Typography variant="body1">{t('courseContent.notFound')}</Typography>
              </Box>
            )}
          </Box>
        );

      case 'video':
        let videoUrl = getVideoUrl(localizedBlock);
        const isYouTubeVideo = localizedBlock.videoContent?.type === 'youtube';

        // Check HLS video URL directly
        let finalVideoUrl = videoUrl;
        if (
          localizedBlock.videoContent?.type === 'hls' &&
          localizedBlock.videoContent?.hlsData?.streamingUrls
        ) {
          const streamingUrls = localizedBlock.videoContent.hlsData.streamingUrls;

          // Case 1: if streamingUrls is an array
          if (Array.isArray(streamingUrls) && streamingUrls.length > 0) {
            const firstUrl = streamingUrls[0];

            // Case 1.1: if first element is an object and has paths property
            if (typeof firstUrl === 'object' && firstUrl !== null && firstUrl.paths) {
              if (Array.isArray(firstUrl.paths) && firstUrl.paths.length > 0) {
                finalVideoUrl = firstUrl.paths[0];
              } else if (typeof firstUrl.paths === 'string') {
                finalVideoUrl = firstUrl.paths;
              }
            }
            // Case 1.2: if first element is directly a string
            else if (typeof firstUrl === 'string') {
              finalVideoUrl = firstUrl;
            }
          }
          // Case 2: if streamingUrls is an object
          else if (typeof streamingUrls === 'object' && streamingUrls !== null) {
            // Case 2.1: if paths property is an array
            if (Array.isArray(streamingUrls.paths) && streamingUrls.paths.length > 0) {
              finalVideoUrl = streamingUrls.paths[0];
            }
            // Case 2.2: if paths property is a string
            else if (typeof streamingUrls.paths === 'string') {
              finalVideoUrl = streamingUrls.paths;
            }
            // Case 2.3: if _id property exists (special case)
            else if (
              streamingUrls._id &&
              Array.isArray(streamingUrls.paths) &&
              streamingUrls.paths.length > 0
            ) {
              finalVideoUrl = streamingUrls.paths[0];
            }
          }
          // Case 3: if streamingUrls is a string
          else if (typeof streamingUrls === 'string') {
            finalVideoUrl = streamingUrls;
          }
        }

        // Extract subtitle data from course structure
        const subtitleData = getSubtitleData(block);

        return (
          <Box sx={{ p: 2 }}>
            {finalVideoUrl ? (
              <Box className="topic-content__video-container">
                {isYouTubeVideo ? (
                  <Box className="youtube-wrapper">
                    <YouTube
                      videoId={getYouTubeVideoId(finalVideoUrl)}
                      opts={{
                        height: '100%',
                        width: '100%',
                        playerVars: {
                          autoplay: 0,
                          controls: 1,
                          modestbranding: 1,
                          rel: 0,
                          origin: window.location.origin,
                          enablejsapi: 1,
                        },
                      }}
                      onEnd={handleVideoEnd}
                      onStateChange={(event) => {
                        if (event.data === YouTube.PlayerState.ENDED) {
                          handleVideoEnd();
                        }
                      }}
                      onError={(error) => {
                        // Handle YouTube error silently
                        console.error('YouTube error:', error);
                      }}
                    />
                  </Box>
                ) : (
                  <EnhancedVideoPlayer
                    videoUrl={finalVideoUrl}
                    thumbnailUrl={localizedBlock.videoContent?.thumbnail || ''}
                    onComplete={handleVideoEnd}
                    courseId={courseId}
                    chapterIndex={chapterIndex}
                    topicIndex={topicIndex}
                    onTimeUpdate={onTimeUpdate}
                    subtitles={subtitleData}
                  />
                )}
              </Box>
            ) : (
              <Box sx={{ p: 2, textAlign: 'center', color: 'error.main' }}>
                <Typography variant="body1">{t('courseContent.videoUrlNotFound')}</Typography>
                <Typography variant="body2">
                  Video content: {JSON.stringify(localizedBlock.videoContent, null, 2)}
                </Typography>
              </Box>
            )}
          </Box>
        );

      case 'quiz':
        return (
          <Box sx={{ p: 3 }} className="content-block__quiz-container">
            <Quiz
              quiz={localizedBlock}
              onComplete={handleQuizComplete}
              courseId={courseId}
              chapterIndex={chapterIndex}
              topicIndex={topicIndex}
              onScoreChange={handleQuizScoreChange}
              isCompleted={isTopicCompleted}
              availableLanguages={Object.keys(block?.translations || {})}
            />
          </Box>
        );

      case 'pdf':
        let pdfUrl = null;
        const translatedBlock = useMemo(() => {
          // First check if translation exists in current language
          const currentLangTranslation = localizedBlock?.translations?.[currentLanguage];
          if (currentLangTranslation) return currentLangTranslation;

          // Otherwise fall back to English translation
          return localizedBlock?.translations?.['en'] || localizedBlock;
        }, [localizedBlock, currentLanguage]);

        if (translatedBlock.pdfContent?.url) {
          pdfUrl = processPdfUrl(translatedBlock.pdfContent.url);
        } else if (translatedBlock.textContent?.content) {
          const hrefMatch = translatedBlock.textContent.content.match(/href=["']([^"']+)["']/);
          if (hrefMatch && hrefMatch[1]) {
            pdfUrl = processPdfUrl(hrefMatch[1]);
          }
        } else {
          if (localizedBlock.pdfContent?.url) {
            pdfUrl = processPdfUrl(localizedBlock.pdfContent.url);
          } else if (localizedBlock.textContent?.content) {
            const hrefMatch = localizedBlock.textContent.content.match(/href=["']([^"']+)["']/);
            if (hrefMatch && hrefMatch[1]) {
              pdfUrl = processPdfUrl(hrefMatch[1]);
            }
          }
        }

        return (
          <PdfViewer
            pdfUrl={pdfUrl}
            title={localizedBlock.title}
            description={localizedBlock.description}
          />
        );

      case 'form':
        return (
          <Box sx={{ p: 3 }}>
            {formSubmitted ? (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <Typography variant="h6" color="primary" gutterBottom>
                  {t('common.thankYou')}
                </Typography>
                <Typography variant="body1">{t('courseContent.formSubmittedSuccess')}</Typography>
              </Box>
            ) : (
              /* Form Content */
              <Box component="form" className="content-block__form" onSubmit={handleFormSubmit}>
                {Array.isArray(formData?.topics) && formData?.topics?.length > 0 ? (
                  formData.topics.map((topic, topicIndex) => (
                    <Box key={topic._id || topicIndex}>
                      <Typography
                        variant="h6"
                        sx={{ mb: Array.isArray(topic.fields) && topic.fields.length > 0 ? 2 : 0 }}
                      >
                        {topic.title}
                      </Typography>
                      {topic.description && (
                        <Typography variant="body2" sx={{ mb: 2 }}>
                          {topic.description}
                        </Typography>
                      )}
                      {Array.isArray(topic.fields) &&
                        topic.fields.length > 0 &&
                        topic.fields.map((field, fieldIndex) => renderField(field))}
                    </Box>
                  ))
                ) : (
                  <Typography color="text.secondary">{t('courseContent.noFieldsFound')}</Typography>
                )}

                {/* Submit Button */}
                <Button
                  type="submit"
                  onClick={handleFormSubmit}
                  variant="contained"
                  className="content-block__form-button"
                  color="primary"
                  sx={{ textTransform: 'capitalize' }}
                  disabled={formSubmitting || formSubmitted}
                >
                  {formSubmitting ? (
                    <>
                      <CircularProgress size={24} sx={{ mr: 1 }} />
                      {t('common.submitting')}
                    </>
                  ) : (
                    t('common.submit')
                  )}
                </Button>
              </Box>
            )}
          </Box>
        );

      default:
        return null;
    }
  };

  // Function to be called when video is completed
  const handleVideoEnd = () => {
    if (onVideoEnded) {
      try {
        onVideoEnded(block._id);
      } catch (error) {
        console.error('Error in handleVideoEnd:', error);
      }
    }
  };

  // Function to be called when quiz is completed
  const handleQuizComplete = (score, totalPoints, passingScore) => {
    if (onQuizComplete) {
      try {
        onQuizComplete(block._id, score, totalPoints, passingScore);
      } catch (error) {
        console.error('Error in handleQuizComplete:', error);
      }
    }
  };

  // Function to be called when we change quiz score
  const handleQuizScoreChange = (score, passingScore) => {
    if (onQuizScoreChange) {
      try {
        onQuizScoreChange(block._id, score, passingScore);
      } catch (error) {
        console.error('Error in handleQuizScoreChange:', error);
      }
    }
  };

  // Function to be called when interactive content is completed
  const handleInteractiveComplete = () => {
    if (onContentCompleted) {
      try {
        onContentCompleted();
      } catch (error) {
        console.error('Error in handleInteractiveComplete:', error);
      }
    }
  };

  return <>{renderContent()}</>;
};

ContentBlock.propTypes = {
  block: PropTypes.object.isRequired,
  courseId: PropTypes.string.isRequired,
  chapterIndex: PropTypes.string,
  topicIndex: PropTypes.string,
  onQuizComplete: PropTypes.func,
  onQuizScoreChange: PropTypes.func,
  isTopicCompleted: PropTypes.bool,
  onVideoEnded: PropTypes.func,
  onTimeUpdate: PropTypes.func,
  onUsecaseGenerate: PropTypes.func,
  onContentCompleted: PropTypes.func,
  onFormSubmit: PropTypes.func,
};

export default ContentBlock;
