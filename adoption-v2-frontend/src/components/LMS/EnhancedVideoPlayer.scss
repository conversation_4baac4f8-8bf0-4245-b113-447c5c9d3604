@use '../../styles/abstracts/variables' as *;

.video-player-container {
  width: 100%;
  position: relative;
  
  &.fullscreen-video-container {
    position: fixed !important;
    top: 0;
    left: 0;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999;
    background-color: #000;
    display: flex;
    flex-direction: column;
    justify-content: center;
    
    .video-wrapper {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 0 !important;
    }
    
    .main-video {
      width: 100% !important;
      height: 100% !important;
      max-height: 100vh !important;
      object-fit: contain !important;
    }
    
    .video-controls {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 10000;
      border-radius: 0 !important;
      background: linear-gradient(to top, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.8) 40%, rgba(0, 0, 0, 0.5) 70%, rgba(0, 0, 0, 0) 100%);
      transform: translateY(100%);
      
      &.visible {
        transform: translateY(0);
        opacity: 1;
      }
    }
  }
  
  &.attempting-fullscreen {
    user-select: none;
    pointer-events: none;
    
    * {
      user-select: none;
      pointer-events: none;
    }
  }

  .video-error {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.7);
    color: #fff;
    z-index: 1;
  }

  .video-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1;
  }

  .video-unavailable {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    min-height: 400px;
    background-color: #f5f5f5;
    border-radius: 4px;
    padding: 16px;
    text-align: center;
  }

  .video-wrapper {
    position: relative;
    width: 100%;
    border-radius: 4px;
    cursor: pointer;
    overflow: hidden;

    &:hover .video-controls {
      opacity: 1;
      transform: translateY(0);
    }

    // Custom subtitle overlay styles
    .subtitle-overlay {
      position: absolute;
      bottom: 80px;
      left: 50%;
      transform: translateX(-50%);
      background-color: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 8px 16px;
      border-radius: 4px;
      font-size: 24px;
      font-weight: bold;
      text-align: center;
      max-width: 80%;
      word-wrap: break-word;
      line-height: 1.4;
      z-index: 10;
      pointer-events: none;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
      
      // Responsive font size
      @media (max-width: 768px) {
        font-size: 14px;
        padding: 6px 12px;
        bottom: 60px;
        max-width: 90%;
      }
      
      // Better visibility in fullscreen
      .fullscreen-video-container & {
        font-size: 20px;
        padding: 12px 20px;
        bottom: 100px;
        
        @media (max-width: 768px) {
          font-size: 16px;
          padding: 8px 16px;
          bottom: 80px;
        }
      }
    }
    
    .main-video {
      width: 100%;
      height: auto;
      aspect-ratio: 16/9;
      object-fit: fill;
      
      &.fullscreen-video {
        width: 100% !important;
        height: 100vh !important;
        max-height: 100vh !important;
        object-fit: contain !important;
        aspect-ratio: unset;
      }
    }

    .center-play-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 3;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: rgba(0, 0, 0, 0.6);
      border-radius: 50%;
      padding: 12px;
      cursor: pointer;
      transition: all 0.3s ease;
      width: 42px;
      height: 42px;

      &.visible {
        display: flex;
      }

      .MuiSvgIcon-root {
        color: #fff;
        font-size: 48px;
        transition: transform 0.3s ease;
      }

      &:hover {
        background-color: rgba(0, 0, 0, 0.8);
        .MuiSvgIcon-root {
          transform: scale(1.1);
        }
      }
    }
  }

  .video-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.8) 40%, rgba(0, 0, 0, 0.5) 70%, rgba(0, 0, 0, 0) 100%);
    padding: 16px;
    transition: opacity 0.3s ease, transform 0.3s ease;
    opacity: 0;
    transform: translateY(100%);

    &.visible, .video-wrapper:hover & {
      opacity: 1;
      transform: translateY(0);
    }
    
   
    .progress-slider {
      color: #fff;
      height: 2px;
      padding: 0;

      .MuiSlider-thumb {
        width: 8px;
        height: 8px;
        transition: 0.2s;
        opacity: 0;

        &:hover, &.Mui-focusVisible {
          box-shadow: 0 0 0 4px rgba(255, 255, 255, 0.16);
          width: 10px;
          height: 10px;
        }
      }

      .MuiSlider-rail {
        opacity: 0.28;
      }

      &:hover {
        .MuiSlider-thumb {
          opacity: 1;
        }
      }
    }

    .controls-wrapper {
      display: flex;
      gap: 8px;
      padding: 0;
      flex-wrap: nowrap;

      .control-button {
        color: white;
        padding: 4px;
        margin: 0 2px;
        
        &:hover {
          background-color: rgba(255, 255, 255, 0.15);
        }
        
        .MuiSvgIcon-root {
          font-size: 1.2rem;
        }
        
        &.subtitle-active {
          color: #1976d2; // primary.main color
        }
      }
      
      .volume-slider-container {
        width: 50px;
        margin: 0 4px;
        
        @media (min-width: 600px) {
          width: 80px;
        }
      }

      .timeline-container {
        flex-grow: 1;
        margin: 0 4px;
      }
      
      .time-display {
        color: white;
        margin: 0 4px;
        white-space: nowrap;
        font-size: 0.75rem;
        min-width: 80px;
        display: flex;
        justify-content: center;
        align-items: center;
        line-height: 1;
        height: 24px;
        
        @media (min-width: 600px) {
          min-width: 100px;
        }
      }

      .volume-slider {
        margin: 0 2px;
        padding: 0;
        color: white;
        height: 2px;
        
        .MuiSlider-rail {
          opacity: 0.28;
        }
        
        .MuiSlider-thumb {
          width: 8px;
          height: 8px;
        }
      }
    }
  }

  .flex-grow {
    flex-grow: 1;
  }

  .playback-menu, .subtitle-menu, .quality-menu {
    margin-top: 8px;
    min-width: 120px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 20000 !important;
  }

  .menu-paper {
    margin-top: 8px;
    min-width: 120px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 20000 !important;
    position: fixed !important;
  }

  .menu-item {
    font-size: 0.95rem;
    padding-top: 8px;
    padding-bottom: 8px;

    &.selected {
      background-color: rgba(0, 0, 0, 0.04);
      font-weight: 500;

      &:hover {
        background-color: rgba(0, 0, 0, 0.08);
      }
    }
  }
  
  @media (max-width: 600px) {
    .controls-wrapper {
      .control-button {
        padding: 2px;
        
        .MuiSvgIcon-root {
          font-size: 1rem;
        }
      }
      
      .time-display {
        font-size: 0.7rem;
        margin: 0 2px;
      }
    }
  }

  .youtube-container {
    position: relative;
    width: 100%;
    padding-top: 56.25%;
    background-color: #000;
  }
  
  .youtube-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 2;
  }
  
  .youtube-error {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    z-index: 2;
    padding: 12px;
    text-align: center;
    
    .youtube-error-message {
      margin-bottom: 8px;
    }
  }
  
  .youtube-player-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
  
  .youtube-player {
    width: 100%;
    height: 100%;
  }
} 

/* Browser-Specific Fullscreen Styles */
:-webkit-full-screen .video-player-container {
  width: 100vw !important;
  height: 100vh !important;
}

:-moz-full-screen .video-player-container {
  width: 100vw !important;
  height: 100vh !important;
}

:-ms-fullscreen .video-player-container {
  width: 100vw !important;
  height: 100vh !important;
}

:fullscreen .video-player-container {
  width: 100vw !important;
  height: 100vh !important;
}

/* Global CSS for menus */
.MuiPopover-root.MuiMenu-root {
  
  .MuiPopover-paper.MuiMenu-paper {
    z-index: 20000 !important;
  }
}

/* Special style for menus in fullscreen mode */
.fullscreen-video-container {
  .subtitle-menu, .playback-menu, .quality-menu {

    z-index: 20000 !important;
  }
  
  /* Position setting for subtitle menu */
  .subtitle-menu .MuiPaper-root {

    z-index: 20000 !important;
  }
}

/* Browser-Specific Fullscreen Menu Styles */
:-webkit-full-screen .MuiPopover-root,
:-moz-full-screen .MuiPopover-root,
:-ms-fullscreen .MuiPopover-root,
:fullscreen .MuiPopover-root {
  z-index: 20000 !important;
  
  .MuiPopover-paper {
    z-index: 20000 !important;
  }
}

.main-video {
  &::cue {
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    font-family: Arial, sans-serif;
    font-size: 25px;
    line-height: 1.5;
    padding: 2px 8px;
    border-radius: 2px;
  }
}


video::-webkit-media-text-track-display {
  padding-bottom: 10vh;
}

/* Fullscreen Menu Styles */
.fullscreen-popup {
  z-index: 99999 !important;
}

.fullscreen-menu-paper {
  z-index: 99999 !important;
}



/* Fix menu visibility in fullscreen */
.MuiModal-root.MuiPopover-root {
  z-index: 99999 !important;
}

