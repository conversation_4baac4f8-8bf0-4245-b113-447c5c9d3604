import React, { useState, useRef, useEffect, forwardRef } from 'react';
import Hls from 'hls.js';
import YouTube from 'react-youtube';
import { useTranslation } from 'react-i18next';
import { CDS_API_URL } from '../../config-global';
import {
  Box,
  Paper,
  Typography,
  IconButton,
  Slider,
  Menu,
  MenuItem,
  Tooltip,
  CircularProgress,
  Button,
  Divider,
} from '@mui/material';
import {
  PlayArrow,
  Pause,
  VolumeUp,
  VolumeOff,
  Speed,
  Fullscreen,
  FullscreenExit,
  Subtitles,
} from '@mui/icons-material';
import './EnhancedVideoPlayer.scss';

const PLAYBACK_SPEEDS = [0.25, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2];

const EnhancedVideoPlayer = forwardRef(
  (
    {
      videoUrl,
      thumbnailUrl,
      onComplete,
      onProgress,
      courseId,
      chapterIndex,
      topicIndex,
      onTimeUpdate,
      onEnded,
      subtitles,
      autoPlay = false,
      preventAutoPlayAfterComplete = false,
      restrictedControls = false,
    },
    ref
  ) => {
    const { t, i18n } = useTranslation();
    const videoRef = useRef(null);
    const hlsRef = useRef(null);
    const playerContainerRef = useRef(null);
    const videoWrapperRef = useRef(null);
    const [playing, setPlaying] = useState(autoPlay && !preventAutoPlayAfterComplete);
    const [volume, setVolume] = useState(1);
    const [muted, setMuted] = useState(false);
    const [played, setPlayed] = useState(0);
    const [seeking, setSeeking] = useState(false);
    const [playbackSpeed, setPlaybackSpeed] = useState(1);
    const [speedMenuAnchor, setSpeedMenuAnchor] = useState(null);
    const [currentTime, setCurrentTime] = useState(0);
    const [duration, setDuration] = useState(0);
    const [isLoading, setIsLoading] = useState(true);
    const [hasError, setHasError] = useState(false);
    const [progressReported, setProgressReported] = useState(false);
    const [isFullscreen, setIsFullscreen] = useState(false);
    const [showControls, setShowControls] = useState(true);
    const [currentSubtitle, setCurrentSubtitle] = useState(null);
    const [userPreferredLanguage, setUserPreferredLanguage] = useState(() => {
      const saved = localStorage.getItem('userSubtitlePreference');
      return saved === 'disabled' ? 'disabled' : saved;
    });
    const [subtitleMenuAnchor, setSubtitleMenuAnchor] = useState(null);
    const [subtitleText, setSubtitleText] = useState('');
    const [subtitleCues, setSubtitleCues] = useState([]);
    const lastKnownPositionRef = useRef(0);
    const positionUpdateTimeoutRef = useRef(null);
    const videoPositionRestored = useRef(false);
    const fullscreenTimeout = useRef(null);
    const controlsTimeoutRef = useRef(null);

    // YouTube video settings
    const youtubeOpts = {
      height: '100%',
      width: '100%',
      playerVars: {
        autoplay: 0,
        controls: 1,
        modestbranding: 1,
        rel: 0,
        origin: window.location.origin,
        enablejsapi: 1,
        playsinline: 1,
        fs: 1,
      },
    };

    // YouTube event handlers
    const handleYouTubeReady = (event) => {
      setIsLoading(false);
      setDuration(event.target.getDuration());
    };

    const handleYouTubeStateChange = (event) => {
      switch (event.data) {
        case YouTube.PlayerState.ENDED:
          if (onComplete) onComplete(1);
          if (onEnded) onEnded();
          break;
        case YouTube.PlayerState.PLAYING:
          setPlaying(true);
          setIsLoading(false);
          break;
        case YouTube.PlayerState.PAUSED:
          setPlaying(false);
          break;
      }
    };

    const handleYouTubeError = (error) => {
      setHasError(true);
    };

    const getYouTubeVideoId = (url) => {
      return url.split('/embed/')[1]?.split('?')[0];
    };

    // HLS configuration
    useEffect(() => {
      if (!videoUrl) return;

      const video = videoRef.current;
      if (!video) return;

      // Safari browser detection
      const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

      const cleanup = () => {
        if (positionUpdateTimeoutRef.current) {
          clearTimeout(positionUpdateTimeoutRef.current);
        }

        if (video) {
          if (video.currentTime > 0) {
            lastKnownPositionRef.current = video.currentTime;
          }
        }

        if (hlsRef.current) {
          hlsRef.current.destroy();
          hlsRef.current = null;
        }
      };

      cleanup();

      videoPositionRestored.current = false;

      const isHLS = videoUrl.includes('.m3u8');

      // Set hasError to false in Safari
      if (isSafari) {
        setHasError(false);
      }

      if (isHLS) {
        if (Hls.isSupported()) {
          const hls = new Hls({
            enableWorker: false,
            debug: false,
            maxBufferLength: 30,
            maxMaxBufferLength: 60,
            backBufferLength: 30,
            startPosition: -1,
            fragLoadingTimeOut: 20000,
            manifestLoadingTimeOut: 20000,
            manifestLoadingMaxRetry: 4,
            levelLoadingTimeOut: 20000,
            fragLoadingMaxRetry: 4,
          });

          hls.loadSource(videoUrl);
          hls.attachMedia(video);

          const safeSetPosition = (position) => {
            if (!video || !position || position <= 0 || videoPositionRestored.current) return;

            try {
              if (position > 0 && position < video.duration) {
                video.currentTime = position;
                videoPositionRestored.current = true;
              }
            } catch (error) {
              // Log removed
            }
          };

          hls.on(Hls.Events.MANIFEST_PARSED, () => {
            setIsLoading(false);
            setHasError(false); // Reset error state

            positionUpdateTimeoutRef.current = setTimeout(() => {
              safeSetPosition(lastKnownPositionRef.current);

              if ((playing || autoPlay) && !preventAutoPlayAfterComplete) {
                video.play().catch(console.error);
              }
            }, 200);
          });

          hls.on(Hls.Events.MEDIA_ATTACHED, () => {
            // Log removed
          });

          hls.on(Hls.Events.FRAG_LOADED, () => {
            if (!videoPositionRestored.current && lastKnownPositionRef.current > 0) {
              positionUpdateTimeoutRef.current = setTimeout(() => {
                safeSetPosition(lastKnownPositionRef.current);
              }, 100);
            }
          });

          hls.on(Hls.Events.LEVEL_LOADED, () => {
            if (!videoPositionRestored.current && lastKnownPositionRef.current > 0) {
              positionUpdateTimeoutRef.current = setTimeout(() => {
                safeSetPosition(lastKnownPositionRef.current);
              }, 100);
            }
          });

          hls.on(Hls.Events.FRAG_CHANGED, () => {
            if (!videoPositionRestored.current && lastKnownPositionRef.current > 0) {
              safeSetPosition(lastKnownPositionRef.current);
            }
          });

          hls.on(Hls.Events.ERROR, (event, data) => {
            // Log removed

            if (video.currentTime > 0) {
              lastKnownPositionRef.current = video.currentTime;
            }

            if (data.fatal) {
              switch (data.type) {
                case Hls.ErrorTypes.NETWORK_ERROR:
                  // Log removed
                  hls.startLoad();
                  break;
                case Hls.ErrorTypes.MEDIA_ERROR:
                  // Log removed
                  hls.recoverMediaError();
                  break;
                default:
                  // Log removed
                  setHasError(true);
                  break;
              }

              videoPositionRestored.current = false;
            }
          });

          hlsRef.current = hls;
        } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
          // Native HLS support for Safari
          setIsLoading(true);
          setHasError(false);

          video.src = videoUrl;

          // Special loading event listener for Safari
          const handleSafariCanPlay = () => {
            setIsLoading(false);
            setHasError(false);

            if (!videoPositionRestored.current && lastKnownPositionRef.current > 0) {
              try {
                video.currentTime = lastKnownPositionRef.current;
                videoPositionRestored.current = true;
              } catch (e) {
                // Hide error when setting time in Safari
              }
            }

            if ((playing || autoPlay) && !preventAutoPlayAfterComplete) {
              const playPromise = video.play();
              if (playPromise !== undefined) {
                playPromise.catch((error) => {
                  // Don't show error if autoplay is blocked
                  if (error.name !== 'NotAllowedError') {
                    console.error('Safari video playback error:', error);
                  }
                });
              }
            }
          };

          // To suppress erroneous video events in Safari
          const handleSafariError = (error) => {
            // Safari may give temporary errors for some HLS videos
            // These errors are usually caused by short-term network issues
            // In this case, we don't set hasError to true
            console.warn('Safari video error event occurred, but will be ignored:', error);

            // Check if video is still playable
            if (video.error && video.error.code === 4) {
              // Show error in case of MEDIA_ERR_SRC_NOT_SUPPORTED
              setHasError(true);
            }
          };

          video.addEventListener('canplay', handleSafariCanPlay, { once: true });

          if (isSafari) {
            video.addEventListener('error', handleSafariError);
          }

          // Safety measure for loading timeout
          const safariLoadTimeout = setTimeout(() => {
            if (isLoading) {
              setIsLoading(false);
            }
          }, 5000);

          return () => {
            clearTimeout(safariLoadTimeout);
            video.removeEventListener('canplay', handleSafariCanPlay);
            if (isSafari) {
              video.removeEventListener('error', handleSafariError);
            }
          };
        }
      } else {
        video.src = videoUrl;

        if (lastKnownPositionRef.current > 0) {
          video.addEventListener(
            'canplay',
            () => {
              if (!videoPositionRestored.current) {
                try {
                  video.currentTime = lastKnownPositionRef.current;
                  videoPositionRestored.current = true;
                } catch (e) {
                  // Error handling for Safari
                  console.warn('Error setting video position:', e);
                }
              }
            },
            { once: true }
          );
        }
      }

      return cleanup;
    }, [videoUrl, autoPlay, preventAutoPlayAfterComplete]);

    // Video events
    useEffect(() => {
      const video = videoRef.current;
      if (!video) return;

      const handleTimeUpdate = () => {
        const currentTime = video.currentTime;
        const duration = video.duration || 0;
        const played = duration ? currentTime / duration : 0;

        if (currentTime > 0) {
          setCurrentTime(currentTime);
          setPlayed(played);
          lastKnownPositionRef.current = currentTime;
        }

        if (onTimeUpdate) {
          onTimeUpdate(currentTime);
        }
      };

      const handleLoadedMetadata = () => {
        setDuration(video.duration);
        setIsLoading(false);
        setHasError(false);

        if (!videoPositionRestored.current && lastKnownPositionRef.current > 0) {
          positionUpdateTimeoutRef.current = setTimeout(() => {
            try {
              video.currentTime = lastKnownPositionRef.current;
              videoPositionRestored.current = true;
            } catch (e) {
              // Error prevention for Safari
            }
          }, 100);
        }

        // Apply subtitle when video metadata is loaded
        if (currentSubtitle) {
          applySubtitle(currentSubtitle);
        }
      };

      const handleCanPlay = () => {
        setIsLoading(false);
        setHasError(false);

        if (!videoPositionRestored.current && lastKnownPositionRef.current > 0) {
          try {
            video.currentTime = lastKnownPositionRef.current;
            videoPositionRestored.current = true;
          } catch (e) {
            // Error prevention for Safari
          }
        }

        // Apply subtitle when video is ready to play
        if (currentSubtitle) {
          applySubtitle(currentSubtitle);
        }
      };

      const handleEnded = () => {
        if (onComplete) {
          onComplete(1);
        }
        if (onEnded) {
          onEnded();
        }
        setPlaying(false);
      };

      const handleError = (error) => {
        console.error('Video error:', error);

        // Check video.error in Safari
        if (video.error) {
          console.error('Video error code:', video.error.code);

          // Don't show error unless it's MEDIA_ERR_SRC_NOT_SUPPORTED (4)
          // Safari sometimes gives temporary errors and then recovers
          if (video.error.code === 4) {
            setHasError(true);
          }
        } else {
          setHasError(true);
        }

        setIsLoading(false);
      };

      const handleSeeking = () => {
        if (video.currentTime > 0) {
          lastKnownPositionRef.current = video.currentTime;
        }
      };

      // Run once to indicate progress will be saved in Safari
      const handleLoadStart = () => {
        setHasError(false);
      };

      video.addEventListener('loadstart', handleLoadStart);
      video.addEventListener('timeupdate', handleTimeUpdate);
      video.addEventListener('loadedmetadata', handleLoadedMetadata);
      video.addEventListener('canplay', handleCanPlay);
      video.addEventListener('ended', handleEnded);
      video.addEventListener('error', handleError);
      video.addEventListener('seeking', handleSeeking);

      return () => {
        video.removeEventListener('loadstart', handleLoadStart);
        video.removeEventListener('timeupdate', handleTimeUpdate);
        video.removeEventListener('loadedmetadata', handleLoadedMetadata);
        video.removeEventListener('canplay', handleCanPlay);
        video.removeEventListener('ended', handleEnded);
        video.removeEventListener('error', handleError);
        video.removeEventListener('seeking', handleSeeking);

        if (video.currentTime > 0) {
          lastKnownPositionRef.current = video.currentTime;
        }

        if (positionUpdateTimeoutRef.current) {
          clearTimeout(positionUpdateTimeoutRef.current);
        }
      };
    }, [onTimeUpdate, onComplete, onEnded]);

    // Playback control
    useEffect(() => {
      const video = videoRef.current;
      if (!video) return;

      if (playing) {
        if (
          lastKnownPositionRef.current > 0 &&
          video.currentTime === 0 &&
          !videoPositionRestored.current
        ) {
          video.currentTime = lastKnownPositionRef.current;
          videoPositionRestored.current = true;
        }

        const playPromise = video.play();
        if (playPromise !== undefined) {
          playPromise.catch((error) => {
            // Log removed
          });
        }
      } else {
        if (video.currentTime > 0) {
          lastKnownPositionRef.current = video.currentTime;
        }
        video.pause();
      }
    }, [playing]);

    // Volume control
    useEffect(() => {
      const video = videoRef.current;
      if (!video) return;

      video.volume = volume;
      video.muted = muted;
    }, [volume, muted]);

    // Playback speed control
    useEffect(() => {
      const video = videoRef.current;
      if (!video) return;

      video.playbackRate = playbackSpeed;
    }, [playbackSpeed]);

    // Fullscreen control
    useEffect(() => {
      const container = playerContainerRef.current;

      const handleFullscreenChange = () => {
        const isFullscreenNow =
          document.fullscreenElement === container ||
          document.webkitFullscreenElement === container ||
          document.mozFullScreenElement === container ||
          document.msFullscreenElement === container;

        setIsFullscreen(isFullscreenNow);

        // Short delay for browser fullscreen APIs to be ready again
        if (!isFullscreenNow) {
          clearTimeout(fullscreenTimeout.current);
          fullscreenTimeout.current = setTimeout(() => {
            // Fix video container dimensions when exiting fullscreen
            if (container) {
              container.classList.remove('fullscreen-video-container');
            }
          }, 300);
        } else if (container) {
          container.classList.add('fullscreen-video-container');
        }
      };

      document.addEventListener('fullscreenchange', handleFullscreenChange);
      document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.addEventListener('mozfullscreenchange', handleFullscreenChange);
      document.addEventListener('MSFullscreenChange', handleFullscreenChange);

      return () => {
        document.removeEventListener('fullscreenchange', handleFullscreenChange);
        document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
        document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
        document.removeEventListener('MSFullscreenChange', handleFullscreenChange);

        if (fullscreenTimeout.current) {
          clearTimeout(fullscreenTimeout.current);
        }
      };
    }, []);

    // Auto-hide controls
    useEffect(() => {
      // Always keep controls visible in restricted controls mode
      if (restrictedControls) {
        setShowControls(true);
        return;
      }

      const startControlsTimeout = () => {
        clearTimeout(controlsTimeoutRef.current);

        if (playing && !seeking) {
          controlsTimeoutRef.current = setTimeout(() => {
            setShowControls(false);
          }, 1000);
        }
      };

      if (playing) {
        startControlsTimeout();
      } else {
        setShowControls(true);
      }

      const handleMouseMove = () => {
        setShowControls(true);
        startControlsTimeout();
      };

      const videoWrapper = videoWrapperRef.current;
      if (videoWrapper) {
        videoWrapper.addEventListener('mousemove', handleMouseMove);
      }

      return () => {
        clearTimeout(controlsTimeoutRef.current);
        if (videoWrapper) {
          videoWrapper.removeEventListener('mousemove', handleMouseMove);
        }
      };
    }, [playing, seeking, restrictedControls]);

    // Auto-loading for subtitles (if English)
    useEffect(() => {
      // Support both subtitle formats:
      // 1. Course/LMS format: { translations: { en: { label: "English", file: "url" } } }
      // 2. Journey format: { en: { label: "English", file: "url" }, es: { label: "Spanish", file: "url" } }

      let availableSubtitles = {};

      if (subtitles?.translations) {
        // Course/LMS format - existing format
        availableSubtitles = subtitles.translations;
      } else if (subtitles && typeof subtitles === 'object' && !subtitles.translations) {
        // Journey format - new format (direct language codes as keys)
        availableSubtitles = subtitles;
      }

      if (Object.keys(availableSubtitles).length > 0) {
        const savedPreference = userPreferredLanguage;

        // If disabled preference is explicitly set, don't load any subtitles
        if (savedPreference === 'disabled') {
          setCurrentSubtitle(null);
          return;
        }

        // If there's a saved preference and it exists in available subtitles
        if (savedPreference && availableSubtitles[savedPreference]) {
          setCurrentSubtitle(availableSubtitles[savedPreference]);
        }
        // If no saved preference or invalid, try current language
        else if (availableSubtitles[i18n.language]) {
          setCurrentSubtitle(availableSubtitles[i18n.language]);
          setUserPreferredLanguage(i18n.language);
        }
        // Fallback to English only if no other option works
        else if (availableSubtitles.en && !currentSubtitle) {
          setCurrentSubtitle(availableSubtitles.en);
          setUserPreferredLanguage('en');
        }
      }
    }, [subtitles, i18n.language, userPreferredLanguage]);

    const handlePlayPause = () => {
      if (videoRef.current && videoRef.current.currentTime > 0) {
        lastKnownPositionRef.current = videoRef.current.currentTime;
      }
      setPlaying(!playing);
    };

    const handleVolumeChange = (event, newValue) => {
      setVolume(newValue);
      setMuted(newValue === 0);
    };

    const handleSeekChange = (event, newValue) => {
      const video = videoRef.current;
      if (!video) return;

      setSeeking(true);

      const newTime = newValue * duration;

      video.currentTime = newTime;
      lastKnownPositionRef.current = newTime;
      videoPositionRestored.current = true;

      setCurrentTime(newTime);
      setPlayed(newValue);
    };

    const handleSeekMouseDown = (event) => {
      setSeeking(true);
    };

    const handleSeekMouseUp = (event, newValue) => {
      setSeeking(false);

      if (!playing) {
        setPlaying(true);
      }
    };

    const handleSpeedChange = (speed) => {
      setPlaybackSpeed(speed);
      setSpeedMenuAnchor(null);
    };

    const handleSubtitleChange = (subtitle) => {
      setCurrentSubtitle(subtitle);
      setSubtitleMenuAnchor(null);

      // Save user's subtitle preference
      if (subtitle) {
        // Support both subtitle formats
        let availableSubtitles = {};
        if (subtitles?.translations) {
          availableSubtitles = subtitles.translations;
        } else if (subtitles && typeof subtitles === 'object' && !subtitles.translations) {
          availableSubtitles = subtitles;
        }

        const languageKey = Object.keys(availableSubtitles).find(
          (key) => availableSubtitles[key] === subtitle
        );
        if (languageKey) {
          localStorage.setItem('userSubtitlePreference', languageKey);
          setUserPreferredLanguage(languageKey);
        }
      } else {
        // Store explicit "disabled" value
        localStorage.setItem('userSubtitlePreference', 'disabled');
        setUserPreferredLanguage('disabled');
      }

      // Apply subtitle using the helper function
      applySubtitle(subtitle);
    };

    const toggleFullscreen = () => {
      if (!playerContainerRef.current) return;

      const container = playerContainerRef.current;

      try {
        if (!isFullscreen) {
          // Prevent any selection or browser behavior
          container.classList.add('attempting-fullscreen');

          // Enter fullscreen
          if (container.requestFullscreen) {
            container.requestFullscreen();
          } else if (container.webkitRequestFullscreen) {
            container.webkitRequestFullscreen();
          } else if (container.mozRequestFullScreen) {
            container.mozRequestFullScreen();
          } else if (container.msRequestFullscreen) {
            container.msRequestFullscreen();
          }
        } else {
          // Exit fullscreen
          if (document.exitFullscreen) {
            document.exitFullscreen();
          } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
          } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen();
          } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
          }

          // Remove class after exiting fullscreen
          setTimeout(() => {
            container.classList.remove('fullscreen-video-container');
            container.classList.remove('attempting-fullscreen');
          }, 100);
        }
      } catch (error) {
        // Log removed
        container.classList.remove('attempting-fullscreen');
        container.classList.remove('fullscreen-video-container');
      }
    };

    const formatTime = (time) => {
      const minutes = Math.floor(time / 60);
      const seconds = Math.floor(time % 60);
      return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    };

    const handleVideoAreaClick = (event) => {
      // Video area click - play/pause toggle
      // Video click disabled in restricted controls mode
      if (!seeking && videoRef.current && !restrictedControls) {
        handlePlayPause();
      }
    };

    const handleControlsClick = (event) => {
      // Prevent control bar click events from propagating to video area
      event.stopPropagation();
    };

    // Helper function to get proxy URL for subtitle files
    const getProxySubtitleUrl = (originalUrl) => {
      if (!originalUrl) return '';

      // Check if it's an Azure Blob Storage URL that needs proxy
      if (originalUrl.includes('aibsassets.blob.core.windows.net')) {
        const encodedUrl = encodeURIComponent(originalUrl);
        const proxyUrl = `${CDS_API_URL}/courses/subtitle-proxy?url=${encodedUrl}`;
        return proxyUrl;
      }

      // Return original URL if it doesn't need proxy
      return originalUrl;
    };

    // Parse VTT content into cues
    const parseVTTContent = (vttContent) => {
      const lines = vttContent.split('\n');
      const cues = [];
      let currentCue = null;
      let isNote = false;

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();

        // Skip empty lines
        if (!line) {
          if (currentCue && currentCue.text) {
            cues.push(currentCue);
            currentCue = null;
          }
          isNote = false;
          continue;
        }

        // Skip WEBVTT header
        if (line.startsWith('WEBVTT')) {
          continue;
        }

        // Skip notes
        if (line.startsWith('NOTE')) {
          isNote = true;
          continue;
        }

        if (isNote) {
          continue;
        }

        // Check if line contains time codes
        const timeMatch = line.match(
          /^(\d{2}:)?(\d{2}):(\d{2})[.,](\d{3})\s*-->\s*(\d{2}:)?(\d{2}):(\d{2})[.,](\d{3})/
        );

        if (timeMatch) {
          // If we have a previous cue, save it
          if (currentCue && currentCue.text) {
            cues.push(currentCue);
          }

          // Parse start time
          const startHours = timeMatch[1] ? parseInt(timeMatch[1].slice(0, -1)) : 0;
          const startMinutes = parseInt(timeMatch[2]);
          const startSeconds = parseInt(timeMatch[3]);
          const startMilliseconds = parseInt(timeMatch[4]);
          const startTime =
            startHours * 3600 + startMinutes * 60 + startSeconds + startMilliseconds / 1000;

          // Parse end time
          const endHours = timeMatch[5] ? parseInt(timeMatch[5].slice(0, -1)) : 0;
          const endMinutes = parseInt(timeMatch[6]);
          const endSecondsNum = parseInt(timeMatch[7]);
          const endMilliseconds = parseInt(timeMatch[8]);
          const endTime =
            endHours * 3600 + endMinutes * 60 + endSecondsNum + endMilliseconds / 1000;

          currentCue = {
            startTime,
            endTime,
            text: '',
          };
        } else if (currentCue) {
          // This is subtitle text
          if (currentCue.text) {
            currentCue.text += '\n' + line;
          } else {
            currentCue.text = line;
          }
        }
      }

      // Add the last cue if exists
      if (currentCue && currentCue.text) {
        cues.push(currentCue);
      }

      return cues;
    };

    // Fetch and load subtitle content
    const loadSubtitleContent = async (subtitle) => {
      try {
        const subtitleUrl = getProxySubtitleUrl(subtitle.file);

        const response = await fetch(subtitleUrl);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const vttContent = await response.text();

        const cues = parseVTTContent(vttContent);
        setSubtitleCues(cues);

        return true;
      } catch (error) {
        console.error('❌ Failed to load subtitle content:', error);
        setSubtitleCues([]);
        return false;
      }
    };

    // Add a helper function to apply subtitle to video
    const applySubtitle = async (subtitle) => {
      const video = videoRef.current;
      if (!video) {
        return;
      }

      // Clear existing subtitle
      setSubtitleText('');
      setSubtitleCues([]);

      if (subtitle) {
        // Load subtitle content using fetch
        await loadSubtitleContent(subtitle);
      }
    };

    // Update subtitle text based on current time and cues
    useEffect(() => {
      if (!currentSubtitle || subtitleCues.length === 0) {
        setSubtitleText('');
        return;
      }

      const activeCue = subtitleCues.find(
        (cue) => currentTime >= cue.startTime && currentTime <= cue.endTime
      );

      if (activeCue) {
        setSubtitleText(activeCue.text);
      } else {
        setSubtitleText('');
      }
    }, [currentTime, subtitleCues, currentSubtitle]);

    return (
      <Box
        sx={{ width: '100%', position: 'relative' }}
        ref={playerContainerRef}
        className={`video-player-container ${isFullscreen ? 'fullscreen-video-container' : ''}`}
      >
        {!videoUrl ? (
          <Box className="video-unavailable">
            <Typography variant="body1" color="text.secondary">
              {t('videoPlayer.videoUnavailable')}
            </Typography>
          </Box>
        ) : videoUrl.includes('youtube.com/embed') ? (
          <Box className="youtube-container">
            {isLoading && (
              <Box className="youtube-loading">
                <CircularProgress color="primary" />
              </Box>
            )}
            {hasError ? (
              <Box className="youtube-error">
                <Typography variant="h6" gutterBottom>
                  {t('videoPlayer.videoNotFound')}
                </Typography>
                <Typography variant="body1" className="youtube-error-message">
                  {t('videoPlayer.videoNotPlayable')}
                </Typography>
                <Button
                  variant="contained"
                  color="primary"
                  href={`https://www.youtube.com/watch?v=${getYouTubeVideoId(videoUrl)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {t('videoPlayer.watchOnYoutube')}
                </Button>
              </Box>
            ) : (
              <Box className="youtube-player-container">
                <YouTube
                  videoId={getYouTubeVideoId(videoUrl)}
                  opts={youtubeOpts}
                  onReady={handleYouTubeReady}
                  onStateChange={handleYouTubeStateChange}
                  onError={handleYouTubeError}
                  className="youtube-player"
                />
              </Box>
            )}
          </Box>
        ) : (
          <Box
            className="video-wrapper"
            ref={videoWrapperRef}
            onMouseEnter={!restrictedControls ? () => setShowControls(true) : undefined}
            onMouseMove={!restrictedControls ? () => setShowControls(true) : undefined}
            onClick={handleVideoAreaClick}
          >
            {isLoading && (
              <Box className="video-loading">
                <CircularProgress color="primary" />
              </Box>
            )}

            <video
              ref={videoRef}
              className={`main-video ${isFullscreen ? 'fullscreen-video' : ''}`}
              poster={thumbnailUrl}
              playsInline
              crossOrigin={videoUrl && videoUrl.includes('.m3u8') ? undefined : 'anonymous'}
              onLoadedData={() => {
                // Video loaded - subtitle system now uses custom overlay
              }}
            />

            {hasError && (
              <Box className="video-error">
                <Typography variant="body1">{t('videoPlayer.videoError')}</Typography>
              </Box>
            )}

            {/* Custom Subtitle Overlay */}
            {subtitleText && (
              <Box className="subtitle-overlay">
                {subtitleText.split('\n').map((line, index) => (
                  <Box key={index} component="div">
                    {line}
                  </Box>
                ))}
              </Box>
            )}

            {/* Controls */}
            <Paper
              className={`video-controls ${showControls ? 'visible' : ''} video-controls-background`}
              onClick={handleControlsClick}
            >
              {/* Control Buttons - Compact Toolbar */}
              <Box className="controls-wrapper">
                {!restrictedControls && (
                  <>
                    {/* 1. Play/Pause */}
                    <IconButton
                      onClick={(e) => {
                        e.stopPropagation();
                        handlePlayPause();
                      }}
                      className="control-button"
                    >
                      {playing ? <Pause /> : <PlayArrow />}
                    </IconButton>

                    {/* 2. Volume control and Mute/Unmute */}
                    <IconButton
                      onClick={(e) => {
                        e.stopPropagation();
                        setMuted(!muted);
                      }}
                      className="control-button"
                    >
                      {muted ? <VolumeOff /> : <VolumeUp />}
                    </IconButton>

                    <Box className="volume-slider-container" onClick={(e) => e.stopPropagation()}>
                      <Slider
                        value={volume}
                        onChange={handleVolumeChange}
                        min={0}
                        max={1}
                        step={0.1}
                        onClick={(e) => e.stopPropagation()}
                        className="volume-slider"
                      />
                    </Box>

                    {/* 3. Duration info */}
                    <Typography variant="caption" className="time-display">
                      {formatTime(currentTime)} / {formatTime(duration)}
                    </Typography>

                    {/* 4. Timeline slider */}
                    <Box className="timeline-container">
                      <Slider
                        value={played}
                        onChange={handleSeekChange}
                        onMouseDown={handleSeekMouseDown}
                        onChangeCommitted={handleSeekMouseUp}
                        min={0}
                        max={1}
                        step={0.001}
                        onClick={(e) => e.stopPropagation()}
                        className="progress-slider"
                      />
                    </Box>

                    {/* 5. Subtitle Button - Changed order */}
                    {(() => {
                      // Support both subtitle formats for button visibility
                      let availableSubtitles = {};
                      if (subtitles?.translations) {
                        availableSubtitles = subtitles.translations;
                      } else if (
                        subtitles &&
                        typeof subtitles === 'object' &&
                        !subtitles.translations
                      ) {
                        availableSubtitles = subtitles;
                      }

                      return (
                        Object.keys(availableSubtitles).length > 0 && (
                          <Tooltip title={t('videoPlayer.subtitles')}>
                            <IconButton
                              onClick={(e) => {
                                e.stopPropagation();
                                setSubtitleMenuAnchor(e.currentTarget);
                              }}
                              className={`control-button ${currentSubtitle ? 'subtitle-active' : ''}`}
                            >
                              <Subtitles fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        )
                      );
                    })()}

                    {/* 6. Playback Speed */}
                    <Tooltip title={t('videoPlayer.playbackSpeed')}>
                      <IconButton
                        onClick={(e) => {
                          e.stopPropagation();
                          setSpeedMenuAnchor(e.currentTarget);
                        }}
                        className="control-button"
                      >
                        <Speed fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </>
                )}

                {/* 7. Fullscreen Button - Always visible */}
                <Tooltip
                  title={
                    isFullscreen ? t('videoPlayer.exitFullscreen') : t('videoPlayer.fullscreen')
                  }
                >
                  <IconButton
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleFullscreen();
                    }}
                    className="control-button"
                  >
                    {isFullscreen ? (
                      <FullscreenExit fontSize="small" />
                    ) : (
                      <Fullscreen fontSize="small" />
                    )}
                  </IconButton>
                </Tooltip>
              </Box>
            </Paper>
          </Box>
        )}

        {/* Playback Speed Menu - Only if not restricted controls */}
        {!restrictedControls && (
          <Menu
            anchorEl={speedMenuAnchor}
            open={Boolean(speedMenuAnchor)}
            onClose={() => setSpeedMenuAnchor(null)}
            PaperProps={{ className: 'menu-paper fullscreen-menu-paper' }}
            className="playback-menu fullscreen-popup"
            keepMounted
            container={playerContainerRef.current}
          >
            {PLAYBACK_SPEEDS.map((speed) => (
              <MenuItem
                key={speed}
                onClick={() => handleSpeedChange(speed)}
                selected={speed === playbackSpeed}
                className={`menu-item ${speed === playbackSpeed ? 'selected' : ''}`}
              >
                {speed}x
              </MenuItem>
            ))}
          </Menu>
        )}

        {/* Subtitle Menu - Only if not restricted controls */}
        {!restrictedControls &&
          (() => {
            // Support both subtitle formats for menu rendering
            let availableSubtitles = {};
            if (subtitles?.translations) {
              availableSubtitles = subtitles.translations;
            } else if (subtitles && typeof subtitles === 'object' && !subtitles.translations) {
              availableSubtitles = subtitles;
            }

            return (
              Object.keys(availableSubtitles).length > 0 && (
                <Menu
                  anchorEl={subtitleMenuAnchor}
                  open={Boolean(subtitleMenuAnchor)}
                  onClose={() => setSubtitleMenuAnchor(null)}
                  PaperProps={{ className: 'menu-paper fullscreen-menu-paper' }}
                  className="subtitle-menu fullscreen-popup"
                  keepMounted
                  container={playerContainerRef.current}
                >
                  <MenuItem
                    onClick={() => handleSubtitleChange(null)}
                    selected={!currentSubtitle}
                    className={`menu-item ${!currentSubtitle ? 'selected' : ''}`}
                  >
                    {t('videoPlayer.disableSubtitles')}
                  </MenuItem>
                  <Divider />
                  {Object.values(availableSubtitles).map((subtitle) => (
                    <MenuItem
                      key={subtitle.label}
                      onClick={() => handleSubtitleChange(subtitle)}
                      selected={currentSubtitle && currentSubtitle.label === subtitle.label}
                      className={`menu-item ${currentSubtitle && currentSubtitle.label === subtitle.label ? 'selected' : ''}`}
                    >
                      {subtitle.label}
                    </MenuItem>
                  ))}
                </Menu>
              )
            );
          })()}
      </Box>
    );
  }
);

EnhancedVideoPlayer.displayName = 'EnhancedVideoPlayer';

export default EnhancedVideoPlayer;
