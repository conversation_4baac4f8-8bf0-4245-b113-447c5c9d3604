import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { selectCurrentUser, selectCurrentToken } from '../../redux/features/auth/authSlice';
import { Box, CircularProgress, Typography } from '@mui/material';
import axios from 'axios';

const PanelRedirect = () => {
  const user = useSelector(selectCurrentUser);
  const authToken = useSelector(selectCurrentToken);
  const token = authToken || localStorage.getItem('accessToken');
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);

  // API URL ortama göre belirlenir
  const getApiUrl = () => {
    if (window.location.hostname === 'localhost') {
      return process.env.VITE_API_URL || 'http://localhost:5000';
    } else if (window.location.hostname.includes('dev')) {
      return 'https://adoptionv2dev.aibusinessschool.com';
    } else if (window.location.hostname.includes('preprod')) {
      return 'https://adoptionv2preprod.aibusinessschool.com';
    } else {
      return 'https://ai.aibusinessschool.com';
    }
  };

  // Panel URL'ini oluştur
  const getPanelUrl = () => {
    if (window.location.hostname === 'localhost') {
      return 'http://localhost:3000/panel/';
    } else if (window.location.hostname.includes('dev')) {
      return 'https://adoptionv2dev.aibusinessschool.com/panel/';
    } else if (window.location.hostname.includes('preprod')) {
      return 'https://adoptionv2preprod.aibusinessschool.com/panel/';
    } else {
      return 'https://ai.aibusinessschool.com/panel/';
    }
  };

  const redirectToPanel = async () => {
    try {
      // Kullanıcı kontrolü
      if (!user || !token) {
        setError('You are not authorized to access the panel.');
        setLoading(false);
        return;
      }

      // Kullanıcının admin yetkisi var mı kontrol et
      if (user.role !== 'admin' && user.role !== 'Administrator') {
        setError('You don\'t have access to the panel.');
        setLoading(false);
        return;
      }

      // Backend'e token ile login isteği gönder
      const apiUrl = getApiUrl();
      const response = await axios.post(
        `${apiUrl}/auth/login-with-token`,
        { token },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data.status === 'success') {
        // Panel oturum bilgilerini al
        const { accessToken } = response.data.data;

        // Panel URL'ini belirle
        const panelUrl = getPanelUrl();

        // Kullanıcı için panel'e yönlendir
        const finalUrl = `${panelUrl}?token=${encodeURIComponent(accessToken)}`;

        // Mevcut sayfada panel uygulamasına yönlendir
        window.location.href = finalUrl;
      } else {
        throw new Error(response.data.message || 'Panel erişimi sağlanamadı');
      }
    } catch (err) {
      console.error('Panel yönlendirme hatası:', err);
      setError(
        err.response?.data?.message || 'Panel uygulamasına erişim sırasında bir hata oluştu.'
      );
      setLoading(false);
    }
  };

  // Sayfa ilk yüklendiğinde otomatik olarak panel'e yönlendir
  useEffect(() => {
    redirectToPanel();
  }, []); // Sadece bir kez çalışsın

  // Sayfa yüklenirken veya hata durumunda gösterilecek içerik
  if (error) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '50vh',
          gap: 2,
        }}
      >
        <Typography variant="h6" color="error">
          {error}
        </Typography>
        <Typography variant="body2">Lütfen giriş yapın veya daha sonra tekrar deneyin.</Typography>
      </Box>
    );
  }

  // Yükleniyor durumu
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '50vh',
        gap: 2,
      }}
    >
      <CircularProgress />
      <Typography variant="body1">Panel uygulamasına yönlendiriliyor...</Typography>
    </Box>
  );
};

export default PanelRedirect;
