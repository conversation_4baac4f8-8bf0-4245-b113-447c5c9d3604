import React from 'react';
import { Card, Typography, Box } from '@mui/material';
import MicrosoftSmall from '../../assets/images/logos/microsoft-small.svg';
import ChatGPTIcon from '../../assets/images/logos/chatgpt.svg';
import WordIcon from '../../assets/images/logos/word.svg';
import OutlookIcon from '../../assets/images/logos/outlook.svg';
import PowerPointIcon from '../../assets/images/logos/powerpoint.svg';
import ExcelIcon from '../../assets/images/logos/excel.svg';
import OneNoteIcon from '../../assets/images/logos/onenote.svg';
import OpenAIIcon from '../../assets/images/logos/openai-logo-vector.svg';
import MSCopilotIcon from '../../assets/images/logos/ms-copilot.svg';
import DallEIcon from '../../assets/images/logos/dall-e.svg';
import FavoriteButton from '@components/FavoriteButton';
import { isMsalConfigured } from '../../utils/msalUtils';
import './PromptCard.scss';

const PromptCard = ({ prompt, onCardClick }) => {
  const { title, createdBy, app: products, tryInMicrosoft, function: functions } = prompt;

  const getProviderIcon = () => {
    switch (createdBy) {
      case 'Microsoft':
        return MicrosoftSmall;
      case 'Open AI':
        return OpenAIIcon;
      default:
        return MSCopilotIcon;
    }
  };

  const getAppIcon = (appName) => {
    switch (appName) {
      case 'Word':
        return WordIcon;
      case 'Outlook':
        return OutlookIcon;
      case 'PowerPoint':
        return PowerPointIcon;
      case 'Excel':
        return ExcelIcon;
      case 'OneNote':
        return OneNoteIcon;
      case 'ChatGPT':
        return ChatGPTIcon;
      case 'Dall-E':
        return DallEIcon;
      default:
        return '';
    }
  };

  const getAppDisplayName = (appName) => {
    switch (appName) {
      case 'Word':
        return 'Microsoft Word';
      case 'Outlook':
        return 'Microsoft Outlook';
      case 'PowerPoint':
        return 'Microsoft PowerPoint';
      case 'Excel':
        return 'Microsoft Excel';
      case 'OneNote':
        return 'Microsoft OneNote';
      default:
        return appName;
    }
  };

  const renderTitle = (text) => {
    if (!text) return null;
    const parts = text.split(/(<placeholder>.*?<\/placeholder>|\[.*?\])/g);
    return (
      <Typography variant="body1" className="prompt-card__title">
        {parts.map((part, index) => {
          if (part.startsWith('<placeholder>') && part.endsWith('</placeholder>')) {
            const content = part.replace('<placeholder>', '').replace('</placeholder>', '').trim();
            return (
              <Box component="span" key={index} className="prompt-card__title-highlight">
                {content ? `{${content}}` : '<placeholder>'}
              </Box>
            );
          } else if (part.startsWith('[') && part.endsWith(']')) {
            const content = part.substring(1, part.length - 1).trim();
            return (
              <Box component="span" key={index} className="prompt-card__title-highlight">
                {`[${content}]`}
              </Box>
            );
          }
          return part;
        })}
      </Typography>
    );
  };

  return (
    <Card
      className="prompt-card"
      elevation={0}
      variant="outlined"
      onClick={() => onCardClick(prompt)}
    >
      <Box className="prompt-card__header">
        <img src={getProviderIcon()} alt="provider" className="prompt-card__provider-icon" />
        <Box onClick={(e) => e.stopPropagation()}>
          <FavoriteButton shortcutID={prompt.id} shortcutType="prompt" iconOnly={true} />
        </Box>
      </Box>

      {renderTitle(title)}

      <Box className="prompt-card__footer">
        {isMsalConfigured() &&
        tryInMicrosoft &&
        products.length === 1 &&
        !products.includes('OneNote') &&
        !products.includes('Outlook') &&
        !products.includes('PowerPoint') ? (
          <Box
            className={`prompt-card__try-in-app prompt-card__try-in-app--${products[0].toLowerCase()}`}
          >
            <img
              src={getAppIcon(products[0])}
              alt={products[0]}
              className="prompt-card__app-icon"
              style={{ width: 16, height: 16 }}
            />
            <Typography
              variant="body2"
              color="primary"
              className={`product-text--${products[0].toLowerCase()}`}
            >
              Try it in {getAppDisplayName(products[0])}
            </Typography>
          </Box>
        ) : (
          <Box className="app-icons-container">
            {/* Eğer Microsoft ürünü ve MSAL yapılandırılmamışsa sadece logo göster */}
            {tryInMicrosoft &&
            products.length === 1 &&
            !isMsalConfigured() &&
            !products.includes('OneNote') &&
            !products.includes('Outlook') &&
            !products.includes('PowerPoint') ? (
              <img
                src={getAppIcon(products[0])}
                alt={products[0]}
                className="prompt-card__app-icon"
              />
            ) : /* Normal durumda tüm logoları göster */
            Array.isArray(products) ? (
              products.map((appName, index) => (
                <img
                  key={index}
                  src={getAppIcon(appName)}
                  alt={appName}
                  className="prompt-card__app-icon"
                />
              ))
            ) : (
              <img src={getAppIcon(products)} alt={products} className="prompt-card__app-icon" />
            )}
          </Box>
        )}
      </Box>
    </Card>
  );
};

export default PromptCard;
