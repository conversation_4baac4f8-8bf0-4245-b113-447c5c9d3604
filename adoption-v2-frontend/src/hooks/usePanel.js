import { useState } from 'react';
import { useSelector } from 'react-redux';
import { selectCurrentUser, selectCurrentToken } from '../redux/features/auth/authSlice';
import axios from 'axios';

const usePanel = () => {
  const user = useSelector(selectCurrentUser);
  const authToken = useSelector(selectCurrentToken);
  const token = authToken || localStorage.getItem('accessToken');
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);

  // API URL ortama göre belirlenir
  const getApiUrl = () => {
    if (window.location.hostname === 'localhost') {
      return process.env.VITE_API_URL || 'http://localhost:5000';
    } else if (window.location.hostname.includes('dev')) {
      return 'https://adoptionv2dev.aibusinessschool.com/api';
    } else if (window.location.hostname.includes('preprod')) {
      return 'https://adoptionv2preprod.aibusinessschool.com/api';
    } else if (window.location.hostname.includes('ai')) {
      return 'https://ai.aibusinessschool.com/api';
    } else {
      return 'https://dentsu.aibusinessschool.com/api';
    }
  };

  // Panel URL'ini oluştur
  const getPanelUrl = () => {
    if (window.location.hostname === 'localhost') {
      return 'http://localhost:3000/panel/';
    } else if (window.location.hostname.includes('dev')) {
      return 'https://adoptionv2dev.aibusinessschool.com/panel/';
    } else if (window.location.hostname.includes('preprod')) {
      return 'https://adoptionv2preprod.aibusinessschool.com/panel/';
    } else if (window.location.hostname.includes('ai')) {
      return 'https://ai.aibusinessschool.com/panel/';
    } else {
      return 'https://dentsu.aibusinessschool.com/panel/';
    }
  };

  const openPanel = async (options = { newTab: true }) => {
    if (!user || !token) {
      setError('You must be logged in to access the panel');
      return false;
    }

    // Kullanıcının admin yetkisi var mı kontrol et
    if (user.role !== 'admin' && user.role !== 'Administrator') {
      setError('You do not have access to the panel');
      return false;
    }

    setLoading(true);
    setError(null);

    try {
      // Backend'e token ile login isteği gönder
      const apiUrl = getApiUrl();
      const response = await axios.post(
        `${apiUrl}/auth/login-with-token`,
        { token },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data.status === 'success') {
        // Panel oturum bilgilerini al
        const { accessToken } = response.data.data;

        // Panel URL'ini belirle
        const panelUrl = getPanelUrl();

        // Kullanıcı için panel'e yönlendir
        const finalUrl = `${panelUrl}?token=${encodeURIComponent(accessToken)}`;

        // Panel uygulamasını aç
        if (options.newTab) {
          window.open(finalUrl, '_blank');
        } else {
          window.location.href = finalUrl;
        }

        setLoading(false);
        return true;
      } else {
        throw new Error(response.data.message || 'Panel access failed');
      }
    } catch (err) {
      console.error('Panel yönlendirme hatası:', err);
      setError(err.response?.data?.message || 'Panel application access error occurred.');
      setLoading(false);
      return false;
    }
  };

  return {
    openPanel,
    loading,
    error,
    hasAccess: user?.role === 'admin' || user?.role === 'Administrator',
    isAuthenticated: !!token && !!user,
  };
};

export default usePanel;
