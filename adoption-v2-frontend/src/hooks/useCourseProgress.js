import { useState, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { API_URL } from '../config-global';
import { useSelector } from 'react-redux';

const useCourseProgress = (course, userId, currentProgressFromProps) => {
  const { i18n } = useTranslation();
  const currentLanguage = i18n.language || 'en';
  const [progressData, setProgressData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const token = useSelector((state) => state.auth.token);

  // Eğer dışarıdan gelen progress varsa, ona öncelik ver
  useEffect(() => {
    if (currentProgressFromProps) {
      setProgressData(currentProgressFromProps);
    }
  }, [currentProgressFromProps]);

  // Kurs ilerleme verisini çek
  useEffect(() => {
    // Eğer dışarıdan progress verilmişse, API çağrısı yapmaya gerek yok
    if (currentProgressFromProps) return;

    const fetchProgress = async () => {
      if (!course?._id || !userId) return;

      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch(
          `${API_URL}/tracking/course/courses/${course._id}/tracking?userId=${userId}`,
          {
            headers: {
              'Content-Type': 'application/json',
              Authorization: `Bearer ${token}`,
            },
            credentials: 'include',
          }
        );

        if (!response.ok) {
          throw new Error('İlerleme verisi alınamadı');
        }

        const result = await response.json();
        setProgressData(result.data);
      } catch (err) {
        setError(err.message);
        console.error('İlerleme verisi alınırken hata:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProgress();
  }, [course?._id, userId, currentProgressFromProps, token]);

  // İlerleme verisini güncelle
  const updateProgress = async ({ chapterId, topicId, completed }) => {
    if (!course?._id || !userId) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`${API_URL}/tracking/course/courses/${course._id}/tracking`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          userId,
          chapterId,
          topicId,
          completed,
          isVideoProgress: false,
        }),
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('İlerleme güncellenemedi');
      }

      const result = await response.json();
      setProgressData(result.data);
    } catch (err) {
      setError(err.message);
      console.error('İlerleme güncellenirken hata:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // İlerleme hesaplamaları
  const progressCalculations = useMemo(() => {
    // Kullanılacak progress verisini belirle: props > state
    const effectiveProgressData = currentProgressFromProps || progressData;

    if (!effectiveProgressData || !course?.translations) {
      return {
        completed: 0,
        total: 0,
        percentage: 0,
        progress: 0,
        isChapterCompleted: () => false,
        isTopicCompleted: () => false,
        isLastTopic: () => false,
        getFirstIncompleteTopicInfo: () => ({ chapterId: null, topicId: null }),
        displayProgress: 0,
        displayCourse: {},
      };
    }

    // Çeviriler için kurs verilerini hazırla
    const translatedContent = course.translations[currentLanguage] || course.translations.en || {};
    const displayCourse = {
      ...course,
      ...translatedContent,
      chapters: translatedContent.chapters || [],
    };

    // Toplam ve tamamlanan konu sayılarını hesapla
    let totalTopics = 0;
    let completedTopics = 0;

    // Toplam konu sayısını hesapla
    const allTopicIds = [];
    displayCourse.chapters?.forEach((chapter) => {
      if (chapter?.topics && Array.isArray(chapter.topics)) {
        totalTopics += chapter.topics.length;
        // Tüm geçerli topic ID'lerini topluyoruz
        chapter.topics.forEach((topic) => {
          allTopicIds.push(topic._id);
        });
      }
    });

    // Progress datasından sadece halen kurs datasında var olan tamamlanan konuları say
    effectiveProgressData.chapters?.forEach((chapterProgress) => {
      chapterProgress.topics?.forEach((topicProgress) => {
        if (topicProgress.completed && allTopicIds.includes(topicProgress.topicId)) {
          completedTopics++;
        }
      });
    });

    // Güvenlik kontrolü
    completedTopics = Math.min(completedTopics, totalTopics);
    // İlerleme yüzdesini hesapla
    const calculatedProgress =
      totalTopics > 0 ? Math.round((completedTopics / totalTopics) * 100) : 0;
    const displayProgress = Math.min(calculatedProgress, 100);

    // Bölüm tamamlanma durumunu kontrol et
    const isChapterCompleted = (chapterId) => {
      const chapterProgress = effectiveProgressData.chapters?.find(
        (progress) => progress.chapterId === chapterId
      );

      // Chapter'ın doğrudan tamamlanma durumu yerine, tüm topic'lerinin tamamlanmış olup olmadığını kontrol edelim
      if (!chapterProgress || !chapterProgress.topics || chapterProgress.topics.length === 0) {
        return false;
      }

      // Önce ilgili chapter'ı bulalım
      const chapter = displayCourse.chapters?.find((ch) => ch._id === chapterId);
      if (!chapter || !chapter.topics || chapter.topics.length === 0) {
        return false;
      }

      // Kurs datasındaki topic'lerin ID'lerini alalım
      const currentTopicIds = chapter.topics.map((topic) => topic._id);

      // Tracking verilerinden yalnızca kurs datasında halen var olan topic'lerin tamamlanma durumlarını kontrol edelim
      const relevantCompletedTopics = chapterProgress.topics.filter(
        (topic) => currentTopicIds.includes(topic.topicId) && topic.completed
      ).length;

      // Kurs datasındaki topic sayısı
      const totalTopicsInChapter = chapter.topics.length;

      // Güncel topic'lerin tamamı tamamlanmışsa true döndürelim
      return relevantCompletedTopics === totalTopicsInChapter;
    };

    // Konu tamamlanma durumunu kontrol et
    const isTopicCompleted = (chapterId, topicId) => {
      const chapterProgress = effectiveProgressData.chapters?.find(
        (progress) => progress.chapterId === chapterId
      );
      const topicProgress = chapterProgress?.topics?.find(
        (progress) => progress.topicId === topicId
      );
      return topicProgress?.completed || false;
    };

    // Son konu kontrolü
    const isLastTopic = (chapterId, topicId) => {
      // Son bölümü bul
      const lastChapterIndex = displayCourse.chapters.length - 1;
      if (lastChapterIndex < 0) return false;

      const lastChapter = displayCourse.chapters[lastChapterIndex];
      if (!lastChapter || !lastChapter.topics || !lastChapter.topics.length) return false;

      // Son konuyu bul
      const lastTopicIndex = lastChapter.topics.length - 1;
      if (lastTopicIndex < 0) return false;

      const lastTopic = lastChapter.topics[lastTopicIndex];

      // Kontrol et
      return lastChapter._id === chapterId && lastTopic._id === topicId;
    };

    // İlk tamamlanmamış topic bilgisini getir
    const getFirstIncompleteTopicInfo = () => {
      // Tüm chapter'ları dön ve ilk tamamlanmamış topicleri bul
      for (const chapter of displayCourse.chapters || []) {
        if (!chapter.topics || !Array.isArray(chapter.topics)) continue;

        // Bu chapter'daki ilerleme verisini bul
        const chapterProgress = effectiveProgressData.chapters?.find(
          (progress) => progress.chapterId === chapter._id
        );

        // Chapter'daki her topic'i kontrol et
        for (const topic of chapter.topics) {
          // Bu topic'in ilerleme verisi var mı?
          const topicProgress = chapterProgress?.topics?.find(
            (progress) => progress.topicId === topic._id
          );

          // Topic tamamlanmamışsa veya ilerleme verisi yoksa bu topic'i döndür
          if (!topicProgress || !topicProgress.completed) {
            return {
              chapterId: chapter._id,
              topicId: topic._id,
            };
          }
        }
      }

      // Eğer tamamlanmamış topic bulunamazsa null döndür
      return { chapterId: null, topicId: null };
    };

    return {
      completed: completedTopics,
      total: totalTopics,
      percentage: displayProgress,
      progress: displayProgress,
      isChapterCompleted,
      isTopicCompleted,
      isLastTopic,
      getFirstIncompleteTopicInfo,
      displayProgress,
      displayCourse,
    };
  }, [course, progressData, currentProgressFromProps, currentLanguage]);

  return {
    ...progressCalculations,
    updateProgress,
    isLoading,
    error,
  };
};

export default useCourseProgress;
