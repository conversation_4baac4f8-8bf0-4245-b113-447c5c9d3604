const UserModel = require("../../models/UserModel");
const FormResponse = require("../../models/FormResponse");
const CompanyModel = require("../../models/CompanyModel");
const ReportModel = require("../../models/ReportModel");
const mongoose = require("mongoose");
const axios = require("axios");
const config = require("../../../config");

// Az-content-api configuration
const AZ_CONTENT_API_URL = config.CDS_URL || "http://localhost:3001/api/v1";
const CDS_API_KEY = config.CDS_API_KEY || "";

exports.generateAIValueReport = async ({ companyId }) => {
  /**
   * Form title and course info for productivity estimates
   * Focus on "AI adoption & productivity survey" form
   * from "Introduction to AI and GenAI" course (Beginner Journey)
   */
  const productivityEstimatesFormTitle = "AI adoption & productivity survey";
  const productivityEstimatesFormId = "67cea57bb8be5c52e8a88a37"; // Keep for form schema fetching
  const introductionCourseId = "67d0363ebbac7c0b9ed65da4"; // Introduction to AI and GenAI course

  /**
   * Form title and course info for expert journey
   * Focus on "Common Form AI value assessment" form (English and German versions)
   * 1st question (Expert Journey)
   */
  const expertJourneyFormTitles = [
    "Common Form AI value assessment", // English
    "Bewertung des Common Form AI-Werts 2" // German
  ];
  const expertJourneyFormId = "67dacf2037f285cd1249f392"; // Correct form ID from CDS

  /**
   * Form title and course info for beginner journey second half
   * Focus on "AI value assessment - Beginner journey" form
   * 1st and 2nd questions (Personal time and job satisfaction)
   */
  const beginnerSecondHalfFormTitle = "AI value assessment - Beginner journey";
  const beginnerSecondHalfFormId = "67d02571c1297ce0b40450cb"; // Form ID from CDS

  try {
    // Check if companyId is valid
    if (!mongoose.Types.ObjectId.isValid(companyId)) {
      return {
        reportType: "ai-value",
        status: "error",
        message: "Invalid companyId format",
        data: null,
      };
    }

    // Ensure mongoose connection is ready
    if (mongoose.connection.readyState !== 1) {
      await mongoose.connect(config.MONGODB_URI, {
        connectTimeoutMS: 5000,
        serverSelectionTimeoutMS: 5000,
      });
    }

    // Find all users in the company with timeout
    const users = await UserModel.find({
      company: new mongoose.Types.ObjectId(companyId),
    }).maxTimeMS(5000);
    const company = await CompanyModel.findById(companyId);

    if (!users || users.length === 0) {
      return {
        reportType: "ai-value",
        status: "error",
        message: "No users found for this company",
        data: null,
      };
    }

    // Create date range for filtering (get all data from last 2 years)
    const endDate = new Date();
    const startDate = new Date();
    startDate.setFullYear(endDate.getFullYear() - 2);

    // Get all user IDs for the company
    const userIds = users.map((user) => user._id);

    // Function to create form schema from responses when CDS API is not available
    const createSchemaFromResponses = (formResponses, questionFilter) => {
      if (!formResponses || formResponses.length === 0) {
        return null;
      }

      // Get all unique field names from responses
      const allFieldNames = new Set();
      formResponses.forEach(response => {
        response.responses.forEach(resp => {
          allFieldNames.add(resp.name);
        });
      });

      const sortedFieldNames = Array.from(allFieldNames).sort();

      // Apply question filter to determine which fields to include
      let fieldsToInclude = sortedFieldNames;
      if (questionFilter) {
        if (questionFilter.type === "range") {
          fieldsToInclude = sortedFieldNames.slice(questionFilter.start, questionFilter.end);
        } else if (questionFilter.type === "specific") {
          fieldsToInclude = questionFilter.indices
            .map((index) => sortedFieldNames[index])
            .filter(Boolean);
        }
      }

      // Create topics based on fields
      const topics = fieldsToInclude.map((fieldName, index) => {
        // Create a generic question title
        const questionTitle = `Question ${index + 1}`;

        // Get all values for this field to create options
        const fieldValues = [];
        formResponses.forEach(response => {
          const fieldResponse = response.responses.find(resp => resp.name === fieldName);
          if (fieldResponse) {
            fieldValues.push(fieldResponse.value);
          }
        });

        const uniqueValues = [...new Set(fieldValues)];
        const options = uniqueValues.map((value, optIndex) => ({
          label: value,
          value: optIndex + 1
        }));

        return {
          title: questionTitle,
          fields: [{
            name: fieldName,
            label: questionTitle,
            type: "radio",
            options: options
          }]
        };
      });

      return {
        title: "Generated Schema",
        topics: topics
      };
    };

    // Function to analyze form responses for specific form titles (supports multiple languages)
    const analyzeFormResponsesByFormTitle = async (
      formTitles, // Can be string or array of strings for multi-language support
      formId, // Still need formId for fetching schema from CDS API
      courseId = null, // Filter by specific course ID instead of journey level
      questionFilter = null
    ) => {
      // questionFilter: { type: 'range', start: 0, end: 2 } or { type: 'specific', indices: [0, 2] }
      // courseId: specific course ID to filter responses

      // Ensure formTitles is an array
      const titleArray = Array.isArray(formTitles) ? formTitles : [formTitles];

      // Build query for form responses using title instead of formId
      const query = {
        submittedBy: { $in: userIds },
        title: { $in: titleArray }, // Support multiple titles for multi-language
        // Remove status filter to include draft responses too
        createdAt: { $gte: startDate, $lte: endDate },
      };

      // Add course ID filter if specified
      if (courseId) {
        const courseObjectId = new mongoose.Types.ObjectId(courseId);
        query["sourceDetails.courseId"] = courseObjectId;
      }

      const formResponses = await FormResponse.find(query).populate(
        "submittedBy",
        "name email department companyDepartment role company"
      );

      if (formResponses.length === 0) {
        return null;
      }

      // Fetch form schema directly from CDS API
      let formSchema = null;

      try {
        const formResponse = await axios.get(
          `${AZ_CONTENT_API_URL}/forms/list/${formId}`,
          {
            headers: {
              "Content-Type": "application/json",
              ...(CDS_API_KEY && { "x-api-key": CDS_API_KEY }),
            },
            timeout: 5000,
          }
        );

        if (
          formResponse.data?.status === "success" &&
          formResponse.data?.data
        ) {
          formSchema = formResponse.data.data;
        }
      } catch (error) {
        // Ignore error, will create schema from responses
      }

      // If no form schema, create a basic schema from form responses
      if (!formSchema) {
        formSchema = createSchemaFromResponses(formResponses, questionFilter);
        if (!formSchema) {
          return null;
        }
      }

      // Analyze responses by questions
      const questionAnalysis = {};

      // Process form schema to extract questions
      if (formSchema) {
        // Use translations.en.topics if available, otherwise use main topics
        let topicsToProcess = [];
        if (formSchema.translations?.en?.topics) {
          topicsToProcess = formSchema.translations.en.topics;
        } else if (formSchema.topics) {
          topicsToProcess = formSchema.topics;
        }

        // Apply question filter if provided
        if (questionFilter) {
          if (questionFilter.type === "range") {
            topicsToProcess = topicsToProcess.slice(
              questionFilter.start,
              questionFilter.end
            );
          } else if (questionFilter.type === "specific") {
            topicsToProcess = questionFilter.indices
              .map((index) => topicsToProcess[index])
              .filter(Boolean);
          }
        }

        // Filter out unwanted questions (Submit Feedback, tool names, etc.)
        topicsToProcess = topicsToProcess.filter((topic) => {
          if (!topic.title) return false;

          const title = topic.title.toLowerCase();

          // Skip feedback and submission topics
          if (title.includes("submit") || title.includes("feedback"))
            return false;

          // Skip tool/wizard names (these typically don't have proper question text)
          if (
            title.includes("wizard") ||
            title.includes("generator") ||
            title.includes("designer") ||
            title.includes("analysis") ||
            title.includes("tactics") ||
            title.includes("ideas")
          )
            return false;

          // Skip if topic doesn't have proper field structure
          if (
            !topic.fields ||
            !Array.isArray(topic.fields) ||
            topic.fields.length === 0
          )
            return false;

          // Only keep topics that look like actual survey questions
          return true;
        });

        topicsToProcess.forEach((topic) => {
          if (topic.fields && Array.isArray(topic.fields)) {
            // Group a/b fields together
            const fieldGroups = {};

            topic.fields.forEach((field) => {
              // Extract base field name and determine field type
              let baseFieldName = field.name;
              let fieldType = 'single';

              // Check for a/b pattern in field names
              if (field.name.includes('_a_') || field.name.includes('_b_')) {
                baseFieldName = field.name.replace(/_[ab]_\d+$/, '');
                fieldType = field.name.includes('_a_') ? 'a' : 'b';
              }
              // Check for a/b pattern in field descriptions
              else if (field.description) {
                const desc = field.description.toLowerCase();
                if (desc.includes('team') && desc.includes('department') || desc.includes('a)')) {
                  fieldType = 'a';
                  baseFieldName = topic.title || field.label || field.name; // Use topic title as base
                } else if (desc.includes('overall') && desc.includes('company') || desc.includes('b)')) {
                  fieldType = 'b';
                  baseFieldName = topic.title || field.label || field.name; // Use topic title as base
                }
              }

              if (!fieldGroups[baseFieldName]) {
                fieldGroups[baseFieldName] = {
                  topic: topic,
                  fields: {},
                  questionText: topic.title || field.label || field.name
                };
              }

              fieldGroups[baseFieldName].fields[fieldType] = field;
            });

            // Process each field group
            Object.keys(fieldGroups).forEach((baseFieldName) => {
              const group = fieldGroups[baseFieldName];
              const questionText = group.questionText;

              if (!questionAnalysis[questionText]) {
                questionAnalysis[questionText] = {
                  questionText: questionText,
                  originalFieldName: baseFieldName,
                  totalResponses: 0,
                  responseBreakdown: {},
                  departmentResponses: {}, // For a options
                  companyResponses: {},    // For b options
                  allPossibleOptions: [],
                  hasABOptions: Object.keys(group.fields).length > 1 && (group.fields.a || group.fields.b)
                };

                // Get all possible options from field schema (use first available field)
                const firstField = group.fields.single || group.fields.a || group.fields.b;
                if (firstField && firstField.options && Array.isArray(firstField.options)) {
                  questionAnalysis[questionText].allPossibleOptions =
                    firstField.options.map((option) => option.label);

                  // Initialize all options with zero count for both a and b
                  firstField.options.forEach((option) => {
                    questionAnalysis[questionText].responseBreakdown[option.label] = 0;
                  });
                }
              }
            });
          }
        });
      }

      // Then, process actual responses
      formResponses.forEach((response) => {
        response.responses.forEach((answer) => {
          // Find which question this field belongs to
          let questionText = null;
          let fieldSchema = null;

          if (formSchema) {
            // Use translations.en.topics if available, otherwise use main topics
            let topicsToCheck = [];
            if (formSchema.translations?.en?.topics) {
              topicsToCheck = formSchema.translations.en.topics;
            } else if (formSchema.topics) {
              topicsToCheck = formSchema.topics;
            }

            // Apply question filter if provided
            if (questionFilter) {
              if (questionFilter.type === "range") {
                topicsToCheck = topicsToCheck.slice(
                  questionFilter.start,
                  questionFilter.end
                );
              } else if (questionFilter.type === "specific") {
                topicsToCheck = questionFilter.indices
                  .map((index) => topicsToCheck[index])
                  .filter(Boolean);
              }
            }

            // Apply the same filtering as in schema processing
            topicsToCheck = topicsToCheck.filter((topic) => {
              if (!topic.title) return false;

              const title = topic.title.toLowerCase();

              // Skip feedback and submission topics
              if (title.includes("submit") || title.includes("feedback"))
                return false;

              // Skip tool/wizard names
              if (
                title.includes("wizard") ||
                title.includes("generator") ||
                title.includes("designer") ||
                title.includes("analysis") ||
                title.includes("tactics") ||
                title.includes("ideas")
              )
                return false;

              // Skip if topic doesn't have proper field structure
              if (
                !topic.fields ||
                !Array.isArray(topic.fields) ||
                topic.fields.length === 0
              )
                return false;

              return true;
            });

            topicsToCheck.forEach((topic) => {
              if (topic.fields && Array.isArray(topic.fields)) {
                const field = topic.fields.find((f) => f.name === answer.name);
                if (field) {
                  questionText = topic.title || field.label || field.name;
                  fieldSchema = field;
                }
              }
            });
          }

          if (!questionText || !questionAnalysis[questionText]) {
            return; // Skip if question not found
          }

          // Get the response label based on the value
          let responseLabel = "Unknown";

          if (fieldSchema && fieldSchema.options) {
            // Remove "value-" prefix if exists
            let cleanValue = answer.value?.toString() || "";
            if (cleanValue.startsWith("value-")) {
              cleanValue = cleanValue.substring(6);
            }

            // Find option by value or by label match
            const option = fieldSchema.options.find(
              (opt) =>
                opt.value === cleanValue ||
                opt.value === answer.value ||
                opt.label === cleanValue
            );

            if (option) {
              responseLabel = option.label;
            } else {
              // If numeric value, try to find by index
              const numericValue = parseInt(cleanValue);
              if (
                !isNaN(numericValue) &&
                fieldSchema.options[numericValue - 1]
              ) {
                responseLabel = fieldSchema.options[numericValue - 1].label;
              } else {
                responseLabel = cleanValue;
              }
            }
          }

          questionAnalysis[questionText].totalResponses += 1;

          // Determine if this is an a or b option
          let isOptionA = answer.name.includes('_a_');
          let isOptionB = answer.name.includes('_b_');

          // If not found by field name, check by field description
          if (!isOptionA && !isOptionB && fieldSchema && fieldSchema.description) {
            const desc = fieldSchema.description.toLowerCase();
            if (desc.includes('team') && desc.includes('department') || desc.includes('a)')) {
              isOptionA = true;
            } else if (desc.includes('overall') && desc.includes('company') || desc.includes('b)')) {
              isOptionB = true;
            }
          }

          // Count response values
          if (
            !questionAnalysis[questionText].responseBreakdown[responseLabel]
          ) {
            questionAnalysis[questionText].responseBreakdown[responseLabel] = 0;
          }
          questionAnalysis[questionText].responseBreakdown[responseLabel] += 1;

          // Department level analysis (for a options) and Company level analysis (for b options)
          const userDepartment =
            response.submittedBy?.department ||
            response.submittedBy?.companyDepartment ||
            response.submittedBy?.role ||
            "Unknown Department";

          if (isOptionA) {
            // Store in departmentResponses for a options
            if (
              !questionAnalysis[questionText].departmentResponses[userDepartment]
            ) {
              questionAnalysis[questionText].departmentResponses[userDepartment] =
                {
                  total: 0,
                  responses: {},
                };

              // Initialize all options for this department
              questionAnalysis[questionText].allPossibleOptions.forEach(
                (optionLabel) => {
                  questionAnalysis[questionText].departmentResponses[
                    userDepartment
                  ].responses[optionLabel] = 0;
                }
              );
            }

            questionAnalysis[questionText].departmentResponses[
              userDepartment
            ].total += 1;

            if (
              !questionAnalysis[questionText].departmentResponses[userDepartment]
                .responses[responseLabel]
            ) {
              questionAnalysis[questionText].departmentResponses[
                userDepartment
              ].responses[responseLabel] = 0;
            }

            questionAnalysis[questionText].departmentResponses[userDepartment]
              .responses[responseLabel] += 1;
          } else if (isOptionB) {
            // Store in companyResponses for b options
            if (!questionAnalysis[questionText].companyResponses[responseLabel]) {
              questionAnalysis[questionText].companyResponses[responseLabel] = {
                count: 0,
                percentage: 0
              };
            }
            questionAnalysis[questionText].companyResponses[responseLabel].count += 1;
          } else {
            // For non-a/b questions, use the original logic
            if (
              !questionAnalysis[questionText].departmentResponses[userDepartment]
            ) {
              questionAnalysis[questionText].departmentResponses[userDepartment] =
                {
                  total: 0,
                  responses: {},
                };

              // Initialize all options for this department
              questionAnalysis[questionText].allPossibleOptions.forEach(
                (optionLabel) => {
                  questionAnalysis[questionText].departmentResponses[
                    userDepartment
                  ].responses[optionLabel] = 0;
                }
              );
            }

            questionAnalysis[questionText].departmentResponses[
              userDepartment
            ].total += 1;

            if (
              !questionAnalysis[questionText].departmentResponses[userDepartment]
                .responses[responseLabel]
            ) {
              questionAnalysis[questionText].departmentResponses[
                userDepartment
              ].responses[responseLabel] = 0;
            }

            questionAnalysis[questionText].departmentResponses[userDepartment]
              .responses[responseLabel] += 1;
          }
        });
      });

      // Calculate percentages
      Object.keys(questionAnalysis).forEach((questionKey) => {
        const question = questionAnalysis[questionKey];

        // For a/b questions, calculate percentages separately
        if (question.hasABOptions) {
          // Company-wide percentages (for b options)
          if (!question.companyPercentages || Object.keys(question.companyPercentages).length === 0) {
            question.companyPercentages = {};
            Object.keys(question.responseBreakdown).forEach((responseLabel) => {
              question.companyPercentages[responseLabel] = {
                count: 0,
                percentage: 0,
              };
            });
          }

          // Calculate percentages for b options
          const totalBResponses = Object.values(question.companyResponses).reduce((sum, resp) => sum + (resp.count || 0), 0);
          Object.keys(question.companyResponses).forEach((responseLabel) => {
            question.companyPercentages[responseLabel] = {
              count: question.companyResponses[responseLabel].count,
              percentage: totalBResponses > 0
                ? Math.round((question.companyResponses[responseLabel].count / totalBResponses) * 100)
                : 0,
            };
          });
        } else {
          // For non-a/b questions, use original logic
          question.companyPercentages = {};
          Object.keys(question.responseBreakdown).forEach((responseLabel) => {
            question.companyPercentages[responseLabel] = {
              count: question.responseBreakdown[responseLabel],
              percentage:
                question.totalResponses > 0
                  ? Math.round(
                      (question.responseBreakdown[responseLabel] /
                        question.totalResponses) *
                        100
                    )
                  : 0,
            };
          });
        }

        // Department percentages (for a options)
        question.departmentPercentages = {};
        Object.keys(question.departmentResponses).forEach((dept) => {
          question.departmentPercentages[dept] = {};
          const deptData = question.departmentResponses[dept];

          Object.keys(deptData.responses).forEach((responseLabel) => {
            question.departmentPercentages[dept][responseLabel] = {
              count: deptData.responses[responseLabel],
              percentage:
                deptData.total > 0
                  ? Math.round(
                      (deptData.responses[responseLabel] / deptData.total) * 100
                    )
                  : 0,
            };
          });
        });
      });

      return {
        formId: formId,
        totalUsers: formResponses.length,
        questions: questionAnalysis,
        responseDate: new Date().toLocaleDateString(),
        dateRange: {
          start: startDate.toLocaleDateString(),
          end: endDate.toLocaleDateString(),
        },
      };
    };

    // Analyze responses for productivity estimates form
    const formAnalyses = {};

    // Productivity Estimates - Questions 1 and 2 from "AI adoption & productivity survey" form
    // Only for Introduction to AI and GenAI course (Beginner journey)
    const productivityEstimatesAnalysis = await analyzeFormResponsesByFormTitle(
      productivityEstimatesFormTitle,
      productivityEstimatesFormId, // For fetching schema from CDS API
      introductionCourseId, // Only from Introduction to AI and GenAI course
      { type: "range", start: 0, end: 2 } // First 2 questions (1a, 1b, 2a, 2b)
    );

    if (productivityEstimatesAnalysis) {
      formAnalyses.productivityEstimates = productivityEstimatesAnalysis;
    }

    // Expert Journey - Question 1 from "Common Form AI value assessment" form (multi-language)
    // No specific course filter (all courses)
    const expertJourneyAnalysis = await analyzeFormResponsesByFormTitle(
      expertJourneyFormTitles, // Array of titles for multi-language support
      expertJourneyFormId, // For fetching schema from CDS API
      null, // No specific course filter
      { type: "range", start: 0, end: 1 } // Only first question
    );

    if (expertJourneyAnalysis) {
      formAnalyses.expertJourney = expertJourneyAnalysis;
    }

    // Personal Productivity - Question 3 from "AI adoption & productivity survey" form
    // Only for Introduction to AI and GenAI course (Beginner journey / first half)
    const personalProductivityAnalysis = await analyzeFormResponsesByFormTitle(
      productivityEstimatesFormTitle,
      productivityEstimatesFormId, // For fetching schema from CDS API
      introductionCourseId, // Only from Introduction to AI and GenAI course
      { type: "range", start: 2, end: 3 } // Only 3rd question (index 2)
    );

    if (personalProductivityAnalysis) {
      formAnalyses.personalProductivity = personalProductivityAnalysis;
    }

    // Beginner Journey Second Half - Questions 1 and 2 from "AI value assessment - Beginner journey" form
    // No specific course filter (all courses)
    const beginnerSecondHalfAnalysis = await analyzeFormResponsesByFormTitle(
      beginnerSecondHalfFormTitle,
      beginnerSecondHalfFormId, // For fetching schema from CDS API
      null, // No specific course filter
      { type: "range", start: 0, end: 2 } // First 2 questions
    );

    if (beginnerSecondHalfAnalysis) {
      formAnalyses.beginnerSecondHalf = beginnerSecondHalfAnalysis;
    }

    // Expert Journey First Half - Questions 2 and 3 from "Common Form AI value assessment" form
    // No specific course filter (all courses)
    const expertFirstHalfAnalysis = await analyzeFormResponsesByFormTitle(
      expertJourneyFormTitles, // Array of titles for multi-language support
      expertJourneyFormId, // For fetching schema from CDS API
      null, // No specific course filter
      { type: "range", start: 1, end: 3 } // Questions 2 and 3 (index 1 and 2)
    );

    if (expertFirstHalfAnalysis) {
      formAnalyses.expertFirstHalf = expertFirstHalfAnalysis;
    }

    // Overall statistics
    const totalRespondents = Object.values(formAnalyses).reduce(
      (total, form) => total + (form.totalUsers || 0),
      0
    );

    const overallStats = {
      totalCompanyUsers: users.length,
      totalRespondents: totalRespondents,
      formsWithResponses: Object.keys(formAnalyses).length,
      reportGeneratedAt: new Date().toISOString(),
      dateRange: {
        start: startDate.toLocaleDateString(),
        end: endDate.toLocaleDateString(),
      },
    };

    // Format data for Excel export (focus on productivity estimates)
    const excelData = {
      title: "AI Adoption Survey / Productivity estimates",
      date: new Date().toLocaleDateString(),
      companyName: company.companyName,
      sections: {},
    };

    // Process form data for Excel format - map to expected course structure
    if (formAnalyses && Object.keys(formAnalyses).length > 0) {
      Object.keys(formAnalyses).forEach((formKey) => {
        const formData = formAnalyses[formKey];
        if (!formData || !formData.questions) return;

        // Map form analyses to expected course keys for Excel compatibility
        let excelSectionKey = "introductionCourseSheet1"; // Default to Introduction Course section
        let sectionTitle = "Beginner Journey";
        let sheetNumber = 1;

        if (formKey === "productivityEstimates") {
          excelSectionKey = "introductionCourseSheet1";
          sectionTitle = "Beginner Journey";
          sheetNumber = 1;
        } else if (formKey === "expertJourney") {
          excelSectionKey = "jobSpecificCourseSheet1"; // Map to Job Specific Course section
          sectionTitle = "Expert Journey";
          sheetNumber = 1;
        } else if (formKey === "personalProductivity") {
          excelSectionKey = "introductionCourseSheet2"; // Map to Personal Productivity section
          sectionTitle = "Beginner journey / first half";
          sheetNumber = 2;
        } else if (formKey === "beginnerSecondHalf") {
          excelSectionKey = "beginnerJourneySecondHalf"; // Map to Beginner Journey Second Half section
          sectionTitle = "Beginner journey / second half";
          sheetNumber = 2;
        } else if (formKey === "expertFirstHalf") {
          excelSectionKey = "expertJourneyFirstHalf"; // Map to Expert Journey First Half section
          sectionTitle = "Expert journey / first half";
          sheetNumber = 2;
        }

        excelData.sections[excelSectionKey] = {
          title: sectionTitle,
          sheetNumber: sheetNumber,
          questions: [],
        };

        Object.keys(formData.questions).forEach((questionKey) => {
          const question = formData.questions[questionKey];
          if (!question) return;

          const questionData = {
            questionText: question.questionText || questionKey,
            departmentData: {},
            companyData: {},
          };

          // Department data (a) in our team / department)
          if (question.departmentPercentages) {
            Object.keys(question.departmentPercentages).forEach((dept) => {
              if (!questionData.departmentData[dept]) {
                questionData.departmentData[dept] = [];
              }
              if (question.departmentPercentages[dept]) {
                Object.keys(question.departmentPercentages[dept]).forEach(
                  (responseLabel) => {
                    const data =
                      question.departmentPercentages[dept][responseLabel];
                    if (data) {
                      questionData.departmentData[dept].push({
                        response: responseLabel,
                        count: data.count || 0,
                        percentage: (data.percentage || 0) + "%",
                      });
                    }
                  }
                );
              }
            });
          }

          // Company data (b) overall in our company)
          if (question.companyPercentages) {
            Object.keys(question.companyPercentages).forEach(
              (responseLabel) => {
                const data = question.companyPercentages[responseLabel];
                if (data) {
                  questionData.companyData[responseLabel] = {
                    count: data.count || 0,
                    percentage: (data.percentage || 0) + "%",
                  };
                }
              }
            );
          }

          excelData.sections[excelSectionKey].questions.push(questionData);
        });
      });
    }

    // Create courseAnalyses structure for backward compatibility with Excel generation
    const courseAnalyses = {};
    if (formAnalyses.productivityEstimates) {
      courseAnalyses.introductionCourseSheet1 = formAnalyses.productivityEstimates;
    }
    if (formAnalyses.expertJourney) {
      courseAnalyses.jobSpecificCourseSheet1 = formAnalyses.expertJourney;
    }
    if (formAnalyses.personalProductivity) {
      courseAnalyses.introductionCourseSheet2 = formAnalyses.personalProductivity;
    }
    if (formAnalyses.beginnerSecondHalf) {
      courseAnalyses.beginnerJourneySecondHalf = formAnalyses.beginnerSecondHalf;
    }
    if (formAnalyses.expertFirstHalf) {
      courseAnalyses.expertJourneyFirstHalf = formAnalyses.expertFirstHalf;
    }

    // Save report to database
    const report = await ReportModel.create({
      companyName: company.companyName,
      company: companyId,
      reportType: "ai-value",
      downloadLink: "not available",
      reportData: {
        courseAnalyses, // Keep this for Excel compatibility
        formAnalyses,   // Keep this for new structure
        overallStats,
        excelData,
      },
      createdAt: new Date(),
    });

    return {
      companyName: company.companyName,
      company: companyId,
      reportType: "ai-value",
      status: "success",
      downloadLink: "not available",
      message: "Success",
      data: {
        courseAnalyses, // Keep this for Excel compatibility
        formAnalyses,   // Keep this for new structure
        overallStats,
        excelData,
        reportId: report._id,
      },
    };
  } catch (err) {
    return {
      reportType: "ai-value",
      status: "error",
      message: `Internal Server Error: ${err.message}`,
      data: null,
    };
  }
};
